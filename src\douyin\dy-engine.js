const { chromium } = require('playwright');
const path = require('path');
const os = require('os');
const fs = require('fs');
const { get, set } = require('../store/store-manager');

/**
 * DoorEngine - 完全按照原项目实现
 * 抖音自动化引擎核心类
 */
class DoorEngine {
  constructor(port, headless = true) {
    this.port = port;
    this.headless = headless;
    this.browser = null;
    this.context = null;
    this.page = null;
    this.monitors = [];
    this.chromePath = null;
    this.userDataDir = null;
  }
  
  /**
   * 获取存储键
   */
  getKey() {
    return `dy_user_${this.port}`;
  }
  
  /**
   * 获取浏览器键
   */
  getBrowserKey() {
    return `dy_user_headless_${this.port}_${this.headless}`;
  }
  
  /**
   * 获取用户数据目录
   */
  getUserDataDir() {
    const userDataPath = path.join(os.homedir(), '.dydz-electron', 'user-data', `port-${this.port}`);
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
    }
    return userDataPath;
  }
  
  /**
   * 初始化引擎（使用新的浏览器实例）
   */
  async init(url = 'https://www.douyin.com/') {
    if (this.browser) {
      await this.browser.close();
    }
    
    this.browser = await this.createBrowser();
    this.context = await this.createContext();
    this.page = await this.context.newPage();
    
    // 设置网络拦截
    await this.setupNetworkInterception(this.page);
    
    // 添加反检测脚本
    await this.addAntiDetectionScript(this.page);
    
    if (url) {
      await this.page.goto(url);
    }
    
    return this.page;
  }
  
  /**
   * 使用持久化上下文初始化
   */
  async initByPersistentContext(url = 'https://www.douyin.com/') {
    if (this.context) {
      await this.context.close();
    }
    
    this.context = await this.createContextByPersistentContext();
    this.page = this.context.pages()[0] || await this.context.newPage();
    
    // 设置网络拦截
    await this.setupNetworkInterception(this.page);
    
    // 添加反检测脚本
    await this.addAntiDetectionScript(this.page);
    
    if (url) {
      await this.page.goto(url);
    }
    
    return this.page;
  }
  
  /**
   * 创建持久化上下文
   */
  async createContextByPersistentContext() {
    const chromePath = await this.getRealChromePath();
    const userDataDir = this.getUserDataDir();
    
    const contextOptions = {
      headless: this.headless,
      executablePath: chromePath,
      userDataDir: userDataDir,
      viewport: { width: 1920, height: 1080 },
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    };
    
    return await chromium.launchPersistentContext(userDataDir, contextOptions);
  }
  
  /**
   * 创建浏览器实例
   */
  async createBrowser() {
    const chromePath = await this.getRealChromePath();
    
    const launchOptions = {
      headless: this.headless,
      executablePath: chromePath,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    };
    
    return await chromium.launch(launchOptions);
  }
  
  /**
   * 创建浏览器上下文
   */
  async createContext() {
    if (!this.browser) {
      throw new Error('浏览器未初始化');
    }
    
    const contextOptions = {
      viewport: { width: 1920, height: 1080 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36'
    };
    
    return await this.browser.newContext(contextOptions);
  }
  
  /**
   * 获取真实的Chrome路径
   */
  async getRealChromePath() {
    if (this.chromePath) {
      return this.chromePath;
    }
    
    // 检查环境变量
    if (process.env.CHROME_PATH) {
      const chromePath = process.env.CHROME_PATH;
      if (fs.existsSync(chromePath)) {
        this.chromePath = chromePath;
        return chromePath;
      }
    }
    
    // 根据操作系统查找Chrome路径
    const platform = os.platform();
    let possiblePaths = [];
    
    switch (platform) {
      case 'win32':
        possiblePaths = [
          'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
          'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
          path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe')
        ];
        break;
      case 'darwin':
        possiblePaths = [
          '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        ];
        break;
      case 'linux':
        possiblePaths = [
          '/usr/bin/google-chrome',
          '/usr/bin/google-chrome-stable',
          '/usr/bin/chromium-browser',
          '/snap/bin/chromium'
        ];
        break;
    }
    
    for (const chromePath of possiblePaths) {
      if (fs.existsSync(chromePath)) {
        this.chromePath = chromePath;
        return chromePath;
      }
    }
    
    // 如果找不到，返回null让Playwright使用默认的
    console.warn('未找到Chrome浏览器，将使用Playwright默认浏览器');
    return null;
  }
  
  /**
   * 获取页面实例
   */
  getPage() {
    return this.page;
  }
  
  /**
   * 添加监控器
   */
  addMonitor(monitor) {
    this.monitors.push(monitor);
  }
  
  /**
   * 启动所有监控器
   */
  async startMonitor() {
    for (const monitor of this.monitors) {
      await monitor.start();
    }
  }
  
  /**
   * 关闭页面
   */
  async closePage() {
    if (this.page) {
      await this.page.close();
      this.page = null;
    }
  }
  
  /**
   * 关闭上下文
   */
  async closeContext() {
    if (this.context) {
      await this.context.close();
      this.context = null;
    }
  }
  
  /**
   * 关闭浏览器
   */
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
  
  /**
   * 释放资源
   */
  async release() {
    await this.closePage();
    await this.closeContext();
    await this.closeBrowser();
    this.monitors = [];
  }

  /**
   * 设置网络拦截
   */
  async setupNetworkInterception(page) {
    await page.route('**/*', async (route) => {
      const request = route.request();
      const headers = await request.allHeaders();

      // 在这里可以修改请求头或拦截特定请求
      await route.continue();
    });

    // 监听响应
    page.on('response', async (response) => {
      await this.doAfterResponse(response);
    });
  }

  /**
   * 处理响应后的逻辑
   */
  async doAfterResponse(response) {
    for (const monitor of this.monitors) {
      if (monitor.finishTag) continue;

      try {
        const matched = await monitor.doMatchResponse(response);
        if (matched) {
          monitor.finishTag = true;
          monitor.eventEmitter.emit(monitor.getEventKey(), response);
        }
      } catch (error) {
        console.error('监控器处理响应时出错:', error);
      }
    }
  }

  /**
   * 添加反检测脚本 - 完全按照原项目实现
   */
  async addAntiDetectionScript(page) {
    await page.addInitScript(() => {
      // 1. 隐藏webdriver属性
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // 2. 伪造WebGL信息
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        // VENDOR
        if (parameter === 37445) {
          return 'Intel Open Source Technology Center';
        }
        // RENDERER
        if (parameter === 37446) {
          return 'Mesa DRI Intel(R) HD Graphics 630';
        }
        return getParameter.apply(this, arguments);
      };

      // 3. 修改Canvas指纹
      const originalGetContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function(type) {
        const context = originalGetContext.apply(this, arguments);
        if (type === '2d' && context) {
          const originalFillText = context.fillText;
          context.fillText = function() {
            const args = Array.from(arguments);
            // 添加微小的随机偏移
            if (args.length >= 3) {
              args[1] += Math.random() * 0.1;
              args[2] += Math.random() * 0.1;
            }
            return originalFillText.apply(this, args);
          };

          const originalGetImageData = context.getImageData;
          context.getImageData = function() {
            const imageData = originalGetImageData.apply(this, arguments);
            // 添加微小的噪点
            for (let i = 0; i < imageData.data.length; i += 4) {
              if (Math.random() < 0.001) {
                imageData.data[i] = Math.floor(Math.random() * 256);
                imageData.data[i + 1] = Math.floor(Math.random() * 256);
                imageData.data[i + 2] = Math.floor(Math.random() * 256);
              }
            }
            return imageData;
          };
        }
        return context;
      };

      // 4. 伪造插件信息
      Object.defineProperty(navigator, 'plugins', {
        get: function() {
          const plugins = [
            {
              name: 'Chrome PDF Plugin',
              filename: 'internal-pdf-viewer',
              description: 'Portable Document Format',
              version: '1.0'
            },
            {
              name: 'Chrome PDF Viewer',
              filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
              description: '',
              version: '1.0'
            },
            {
              name: 'Native Client',
              filename: 'internal-nacl-plugin',
              description: '',
              version: '1.0'
            }
          ];
          plugins.item = function(index) { return this[index]; };
          plugins.namedItem = function(name) { return this.find(p => p.name === name); };
          plugins.refresh = function() {};
          return plugins;
        }
      });

      // 5. 伪造语言设置
      Object.defineProperty(navigator, 'languages', {
        get: function() {
          return ['zh-CN', 'zh', 'en-US', 'en'];
        }
      });

      // 6. 伪造硬件信息
      Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: function() {
          return 8;
        }
      });

      Object.defineProperty(navigator, 'deviceMemory', {
        get: function() {
          return 8;
        }
      });

      // 7. 伪造权限API
      if (navigator.permissions) {
        Object.defineProperty(navigator.permissions, 'query', {
          value: function() {
            return Promise.resolve({ state: 'prompt' });
          }
        });
      }

      // 8. 伪造平台信息
      Object.defineProperty(navigator, 'platform', {
        get: function() {
          return 'Win32';
        }
      });

      // 9. 伪造用户代理
      Object.defineProperty(navigator, 'userAgent', {
        get: function() {
          return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36';
        }
      });

      // 10. 添加Chrome对象
      window.chrome = {
        runtime: {},
        loadTimes: function() {
          return {
            firstPaintTime: 0,
            startLoadTime: Date.now() / 1000,
            commitLoadTime: Date.now() / 1000,
            finishDocumentLoadTime: Date.now() / 1000,
            finishLoadTime: Date.now() / 1000,
            firstPaintAfterLoadTime: 0,
            navigationType: 'Other',
            wasFetchedViaSpdy: false,
            wasNpnNegotiated: false,
            npnNegotiatedProtocol: 'unknown',
            wasAlternateProtocolAvailable: false,
            connectionInfo: 'unknown'
          };
        },
        csi: function() {
          return {
            startE: Date.now(),
            onloadT: Date.now(),
            pageT: Date.now(),
            tran: 15
          };
        },
        app: {
          isInstalled: false,
          getDetails: function() {},
          getIsInstalled: function() {},
          installState: function() { return 'disabled'; },
          runningState: function() { return 'cannot_run'; }
        }
      };

      // 11. 拦截performance.mark等性能检测API
      if (window.performance && window.performance.mark) {
        window.performance.mark = function() {};
      }

      // 12. 伪造屏幕信息
      Object.defineProperty(screen, 'width', {
        get: function() { return 1920; }
      });
      Object.defineProperty(screen, 'height', {
        get: function() { return 1080; }
      });
      Object.defineProperty(screen, 'availWidth', {
        get: function() { return 1920; }
      });
      Object.defineProperty(screen, 'availHeight', {
        get: function() { return 1040; }
      });

      // 13. 伪造时区信息
      Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
        value: function() {
          return {
            locale: 'zh-CN',
            calendar: 'gregory',
            numberingSystem: 'latn',
            timeZone: 'Asia/Shanghai'
          };
        }
      });

      console.log('DoorEngine反检测脚本已加载');
    });
  }
}

module.exports = { DoorEngine };
