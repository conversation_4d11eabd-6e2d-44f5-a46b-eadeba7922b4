const { DoorEngine } = require('./dy-engine');
const { DiggMonitor } = require('./digg-monitor');

// 引擎实例缓存
const engineCache = new Map();
// 监控器实例缓存
const monitorCache = new Map();

/**
 * 获取或创建引擎实例
 * @param {number} port 端口号
 * @param {boolean} headless 是否无头模式
 * @returns {DoorEngine} 引擎实例
 */
function getEngine(port, headless = true) {
  const key = `dy_user_headless_${port}_${headless}`;
  
  if (engineCache.has(key)) {
    return engineCache.get(key);
  }
  
  const engine = new DoorEngine(port, headless);
  engineCache.set(key, engine);
  
  console.log(`创建新的引擎实例: ${key}`);
  return engine;
}

/**
 * 获取或创建点赞监控器实例
 * @param {number} port 端口号
 * @param {boolean} headless 是否无头模式
 * @returns {DiggMonitor} 监控器实例
 */
function getDiggMonitor(port, headless = true) {
  if (monitorCache.has(port)) {
    return monitorCache.get(port);
  }
  
  const monitor = new DiggMonitor(port, headless);
  monitorCache.set(port, monitor);
  
  console.log(`创建新的点赞监控器实例: ${port}`);
  return monitor;
}

/**
 * 移除引擎实例
 * @param {number} port 端口号
 * @param {boolean} headless 是否无头模式
 */
async function removeEngine(port, headless = true) {
  const key = `dy_user_headless_${port}_${headless}`;
  
  if (engineCache.has(key)) {
    const engine = engineCache.get(key);
    await engine.release();
    engineCache.delete(key);
    console.log(`移除引擎实例: ${key}`);
  }
}

/**
 * 移除监控器实例
 * @param {number} port 端口号
 */
function removeDiggMonitor(port) {
  if (monitorCache.has(port)) {
    const monitor = monitorCache.get(port);
    monitor.stop();
    monitorCache.delete(port);
    console.log(`移除点赞监控器实例: ${port}`);
  }
}

/**
 * 清理所有实例
 */
async function clearAllInstances() {
  // 清理所有引擎
  for (const [key, engine] of engineCache) {
    try {
      await engine.release();
    } catch (error) {
      console.error(`清理引擎实例 ${key} 时出错:`, error);
    }
  }
  engineCache.clear();
  
  // 清理所有监控器
  for (const [port, monitor] of monitorCache) {
    try {
      monitor.stop();
    } catch (error) {
      console.error(`清理监控器实例 ${port} 时出错:`, error);
    }
  }
  monitorCache.clear();
  
  console.log('所有实例已清理');
}

/**
 * 获取实例统计信息
 */
function getInstanceStats() {
  return {
    engines: engineCache.size,
    monitors: monitorCache.size,
    engineKeys: Array.from(engineCache.keys()),
    monitorPorts: Array.from(monitorCache.keys())
  };
}

/**
 * 检查端口是否有活跃实例
 * @param {number} port 端口号
 */
function hasActiveInstance(port) {
  const hasMonitor = monitorCache.has(port);
  const hasEngine = Array.from(engineCache.keys()).some(key => key.includes(`_${port}_`));
  
  return hasMonitor || hasEngine;
}

module.exports = {
  getEngine,
  getDiggMonitor,
  removeEngine,
  removeDiggMonitor,
  clearAllInstances,
  getInstanceStats,
  hasActiveInstance
};
