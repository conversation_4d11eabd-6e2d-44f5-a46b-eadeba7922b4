const { app, BrowserWindow } = require('electron');
const path = require('path');
const { registerRpc } = require('./rpc/rpc-register');
const { initStore } = require('./store/store-manager');
const { startExpressServer } = require('./server/express-server');

// 设置进程编码为UTF-8
if (process.platform === 'win32') {
  // 设置控制台代码页为UTF-8
  try {
    const { spawn } = require('child_process');
    spawn('chcp', ['65001'], { stdio: 'inherit', shell: true });
  } catch (error) {
    // 忽略错误
  }
}

let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  // 加载应用的 index.html
  mainWindow.loadFile(path.join(__dirname, '../resources/html/index.html'));

  // 打开开发者工具
  if (process.env.NODE_ENV === 'development' || process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// 这段程序将会在 Electron 结束初始化和创建浏览器窗口的时候调用
app.whenReady().then(async () => {
  // 设置控制台编码为UTF-8
  if (process.platform === 'win32') {
    process.stdout.setEncoding('utf8');
    process.stderr.setEncoding('utf8');
  }

  // 初始化存储
  initStore(app.getPath('userData'));
  console.log('Store manager initialized');

  // 注册RPC通信
  await registerRpc();
  console.log('RPC interface registered');

  // 创建主窗口
  createWindow();
  console.log('Main window created');

  // 启动Express服务器
  try {
    await startExpressServer();
    console.log('SecureExpressServer started successfully on port 23333');
  } catch (error) {
    console.error('Failed to start Express server:', error);
  }

  console.log('Application initialized successfully!');

  app.on('activate', function () {
    // 在macOS上，当点击dock图标并且没有其他窗口打开的时候，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 除了 macOS 外，当所有窗口都被关闭的时候退出程序。
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用 require 导入。
