const { exec } = require('child_process');
const os = require('os');

/**
 * 进程检测器
 * 用于检测系统中是否运行了抓包工具或调试工具
 */
class ProcessDetector {
  constructor() {
    this.isRunning = false;
    this.checkInterval = 10000; // 10秒检查一次
    this.intervalId = null;
    
    // 黑名单进程列表 - 完全按照原项目实现
    this.blacklistedProcesses = [
      // 抓包工具
      'charles',
      'fiddler',
      'burpsuite',
      'burp',
      'wireshark',
      'tcpdump',
      'mitmproxy',
      'proxyman',
      'httpcanary',
      'packet capture',
      'network analyzer',
      'charles proxy',
      'fiddler everywhere',
      'owasp zap',
      'zaproxy',
      'postman interceptor',
      'anyproxy',
      'whistle',
      'lightproxy',
      'proxifier',
      'sockscap',
      'freecap',
      'proxycap',

      // 调试工具
      'cheat engine',
      'process hacker',
      'process monitor',
      'procmon',
      'api monitor',
      'detours',
      'winapi override',
      'spy++',
      'dependency walker',
      'pe explorer',
      'ida pro',
      'ollydbg',
      'x64dbg',
      'immunity debugger',
      'windbg',
      'gdb',
      'lldb',

      // 逆向工具
      'ghidra',
      'radare2',
      'hopper',
      'binary ninja',
      'dnspy',
      'reflexil',
      'de4dot',
      'ilspy',

      // 虚拟机检测
      'vmware',
      'virtualbox',
      'vbox',
      'qemu',
      'hyper-v',
      'parallels',

      // 沙箱检测
      'sandboxie',
      'cuckoo',
      'anubis',
      'joebox',
      'threatanalyzer'
    ];
  }
  
  /**
   * 启动进程检测
   */
  start() {
    if (this.isRunning) {
      console.log('进程检测器已在运行');
      return;
    }
    
    this.isRunning = true;
    console.log('启动进程检测器');
    
    // 立即执行一次检测
    this.checkProcesses();
    
    // 设置定时检测
    this.intervalId = setInterval(() => {
      this.checkProcesses();
    }, this.checkInterval);
  }
  
  /**
   * 停止进程检测
   */
  stop() {
    if (!this.isRunning) {
      console.log('进程检测器未运行');
      return;
    }
    
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    console.log('进程检测器已停止');
  }
  
  /**
   * 检查系统进程
   */
  async checkProcesses() {
    try {
      const processes = await this.getRunningProcesses();
      const suspiciousProcesses = this.findSuspiciousProcesses(processes);
      
      if (suspiciousProcesses.length > 0) {
        console.warn('检测到可疑进程:', suspiciousProcesses);
        this.handleSuspiciousProcesses(suspiciousProcesses);
      }
    } catch (error) {
      console.error('检查进程时出错:', error);
    }
  }
  
  /**
   * 获取运行中的进程列表
   */
  getRunningProcesses() {
    return new Promise((resolve, reject) => {
      const platform = os.platform();
      let command;
      
      switch (platform) {
        case 'win32':
          command = 'tasklist /fo csv';
          break;
        case 'darwin':
          command = 'ps -ax -o comm';
          break;
        case 'linux':
          command = 'ps -ax -o comm';
          break;
        default:
          reject(new Error(`不支持的操作系统: ${platform}`));
          return;
      }
      
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }
        
        const processes = this.parseProcessList(stdout, platform);
        resolve(processes);
      });
    });
  }
  
  /**
   * 解析进程列表
   */
  parseProcessList(output, platform) {
    const processes = [];
    const lines = output.split('\n');
    
    switch (platform) {
      case 'win32':
        // Windows tasklist CSV格式
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const parts = line.split(',');
            if (parts.length > 0) {
              const processName = parts[0].replace(/"/g, '').toLowerCase();
              processes.push(processName);
            }
          }
        }
        break;
      
      case 'darwin':
      case 'linux':
        // macOS/Linux ps格式
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const processName = line.toLowerCase();
            processes.push(processName);
          }
        }
        break;
    }
    
    return processes;
  }
  
  /**
   * 查找可疑进程
   */
  findSuspiciousProcesses(processes) {
    const suspicious = [];
    
    for (const process of processes) {
      for (const blacklisted of this.blacklistedProcesses) {
        if (process.includes(blacklisted)) {
          suspicious.push(process);
          break;
        }
      }
    }
    
    return suspicious;
  }
  
  /**
   * 处理可疑进程 - 完全按照原项目实现
   */
  handleSuspiciousProcesses(suspiciousProcesses) {
    console.error('ProcessDetector检测到以下可疑进程:', suspiciousProcesses);

    // 记录检测到的进程
    const logMessage = `检测时间: ${new Date().toISOString()}\n可疑进程: ${suspiciousProcesses.join(', ')}`;
    console.error(logMessage);

    // 在生产环境中，立即退出应用
    if (process.env.NODE_ENV === 'production' && !process.argv.includes('--dev')) {
      console.error('检测到调试/抓包工具，为保护软件安全，应用将立即退出');

      // 显示警告对话框（如果在Electron环境中）
      try {
        const { dialog } = require('electron');
        dialog.showErrorBox(
          '安全警告',
          '检测到调试或抓包工具正在运行，为保护软件安全，应用将退出。\n\n请关闭以下工具后重新启动：\n' + suspiciousProcesses.join('\n')
        );
      } catch (error) {
        // 如果不在Electron环境中，忽略错误
      }

      // 立即退出
      process.exit(1);
    } else {
      console.warn('开发环境中检测到调试工具，但不会退出应用');
      console.warn('生产环境中将会立即退出应用');
    }
  }
  
  /**
   * 设置检查间隔
   */
  setCheckInterval(interval) {
    this.checkInterval = interval;
    
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }
  
  /**
   * 添加黑名单进程
   */
  addBlacklistedProcess(processName) {
    if (!this.blacklistedProcesses.includes(processName.toLowerCase())) {
      this.blacklistedProcesses.push(processName.toLowerCase());
      console.log(`已添加黑名单进程: ${processName}`);
    }
  }
  
  /**
   * 移除黑名单进程
   */
  removeBlacklistedProcess(processName) {
    const index = this.blacklistedProcesses.indexOf(processName.toLowerCase());
    if (index > -1) {
      this.blacklistedProcesses.splice(index, 1);
      console.log(`已移除黑名单进程: ${processName}`);
    }
  }
  
  /**
   * 获取黑名单进程列表
   */
  getBlacklistedProcesses() {
    return [...this.blacklistedProcesses];
  }
}

// 单例实例
const processDetector = new ProcessDetector();

module.exports = { ProcessDetector, processDetector };
