const { HttpCryptoService } = require('./crypto-service');

/**
 * 安全中间件 - 完全按照原项目实现
 * 用于加密和解密HTTP请求和响应
 */
const secureMiddleware = (req, res, next) => {
  // 处理原始请求体
  if (req.method === 'POST' && req.headers['content-type'] === 'application/json') {
    let body = '';

    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        // 检查是否为加密数据
        if (body && typeof body === 'string' && HttpCryptoService.isEncrypted(body.replace(/"/g, ''))) {
          const cleanBody = body.replace(/"/g, '');
          const decryptedData = HttpCryptoService.decrypt(cleanBody);
          req.body = decryptedData;
          console.log('请求体解密成功');
        } else {
          // 如果不是加密数据，尝试解析为JSON
          req.body = body ? JSON.parse(body) : {};
        }

        continueProcessing();
      } catch (error) {
        console.error('请求体处理失败:', error);
        return res.status(400).json({ error: '请求数据格式错误' });
      }
    });

    function continueProcessing() {
      setupResponseInterception();
      next();
    }
  } else {
    setupResponseInterception();
    next();
  }

  function setupResponseInterception() {

  // 拦截响应
  const originalSend = res.send;
  const originalJson = res.json;

  // 重写send方法
  res.send = function(data) {
    try {
      if (typeof data === 'object') {
        data = JSON.stringify(data);
      }

      // 加密响应数据
      const encryptedData = HttpCryptoService.encrypt(data);

      // 设置响应头
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('X-Encrypted', 'true');

      console.log('响应数据加密成功');
      return originalSend.call(this, encryptedData);
    } catch (error) {
      console.error('响应数据加密失败:', error);
      return originalSend.call(this, JSON.stringify({ error: '响应数据处理失败' }));
    }
  };

  // 重写json方法
  res.json = function(data) {
    try {
      const jsonString = JSON.stringify(data);

      // 加密响应数据
      const encryptedData = HttpCryptoService.encrypt(jsonString);

      // 设置响应头
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('X-Encrypted', 'true');

      console.log('JSON响应数据加密成功');
      return originalSend.call(this, encryptedData);
    } catch (error) {
      console.error('JSON响应数据加密失败:', error);
      return originalJson.call(this, { error: '响应数据处理失败' });
    }
  };

  next();
};

/**
 * 创建签名验证中间件
 * 用于验证请求的完整性
 */
function createSignatureMiddleware() {
  return (req, res, next) => {
    const signature = req.headers['x-signature'];
    const timestamp = req.headers['x-timestamp'];
    
    if (!signature || !timestamp) {
      return res.status(401).json({ error: '缺少签名或时间戳' });
    }
    
    // 检查时间戳是否在有效范围内（5分钟）
    const now = Date.now();
    const requestTime = parseInt(timestamp);
    const timeDiff = Math.abs(now - requestTime);
    
    if (timeDiff > 5 * 60 * 1000) {
      return res.status(401).json({ error: '请求时间戳无效' });
    }
    
    // 构造签名数据
    const method = req.method;
    const url = req.originalUrl;
    const body = req.body ? JSON.stringify(req.body) : '';
    const signatureData = `${method}${url}${body}${timestamp}`;
    
    // 验证签名
    if (!cryptoService.verify(signatureData, signature)) {
      return res.status(401).json({ error: '签名验证失败' });
    }
    
    console.log('签名验证成功');
    next();
  };
}

/**
 * 创建API密钥验证中间件
 */
function createApiKeyMiddleware(validApiKeys = []) {
  return (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({ error: '缺少API密钥' });
    }
    
    if (validApiKeys.length > 0 && !validApiKeys.includes(apiKey)) {
      return res.status(401).json({ error: 'API密钥无效' });
    }
    
    console.log('API密钥验证成功');
    next();
  };
}

/**
 * 创建速率限制中间件
 */
function createRateLimitMiddleware(maxRequests = 100, windowMs = 60000) {
  const requests = new Map();
  
  return (req, res, next) => {
    const clientId = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    // 清理过期记录
    for (const [id, data] of requests) {
      if (now - data.firstRequest > windowMs) {
        requests.delete(id);
      }
    }
    
    // 检查当前客户端的请求次数
    if (!requests.has(clientId)) {
      requests.set(clientId, {
        count: 1,
        firstRequest: now
      });
    } else {
      const clientData = requests.get(clientId);
      
      if (now - clientData.firstRequest < windowMs) {
        clientData.count++;
        
        if (clientData.count > maxRequests) {
          return res.status(429).json({ 
            error: '请求过于频繁，请稍后再试',
            retryAfter: Math.ceil((windowMs - (now - clientData.firstRequest)) / 1000)
          });
        }
      } else {
        // 重置计数
        clientData.count = 1;
        clientData.firstRequest = now;
      }
    }
    
    next();
  };
}

/**
 * 创建安全中间件
 */
function createSecureMiddleware() {
  return secureMiddleware;
}

module.exports = {
  createSecureMiddleware,
  secureMiddleware
};
