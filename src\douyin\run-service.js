const { getEngine, getDiggMonitor } = require('./engine-manager');

/**
 * 根据端口启动运行服务
 * @param {number} port 端口号
 * @param {boolean} headless 是否无头模式
 */
async function runByPort(port, headless = true) {
  try {
    console.log(`启动运行服务，端口: ${port}, 无头模式: ${headless}`);
    
    // 获取引擎实例
    const engine = getEngine(port, headless);
    
    // 初始化引擎（使用持久化上下文）
    await engine.initByPersistentContext();
    
    // 获取点赞监控器
    const diggMonitor = getDiggMonitor(port, headless);
    
    // 启动点赞监控
    await diggMonitor.start(engine);
    
    console.log(`运行服务启动成功，端口: ${port}`);
    
    return {
      success: true,
      port: port,
      headless: headless,
      message: '运行服务启动成功'
    };
  } catch (error) {
    console.error(`启动运行服务失败，端口: ${port}`, error);
    
    return {
      success: false,
      port: port,
      error: error.message,
      message: '运行服务启动失败'
    };
  }
}

/**
 * 停止指定端口的运行服务
 * @param {number} port 端口号
 * @param {boolean} headless 是否无头模式
 */
async function stopByPort(port, headless = true) {
  try {
    console.log(`停止运行服务，端口: ${port}`);
    
    const { removeEngine, removeDiggMonitor } = require('./engine-manager');
    
    // 移除监控器
    removeDiggMonitor(port);
    
    // 移除引擎
    await removeEngine(port, headless);
    
    console.log(`运行服务停止成功，端口: ${port}`);
    
    return {
      success: true,
      port: port,
      message: '运行服务停止成功'
    };
  } catch (error) {
    console.error(`停止运行服务失败，端口: ${port}`, error);
    
    return {
      success: false,
      port: port,
      error: error.message,
      message: '运行服务停止失败'
    };
  }
}

/**
 * 获取运行服务状态
 * @param {number} port 端口号
 */
function getRunStatus(port) {
  const { hasActiveInstance } = require('./engine-manager');
  
  return {
    port: port,
    active: hasActiveInstance(port),
    timestamp: new Date().toISOString()
  };
}

/**
 * 获取所有运行服务状态
 */
function getAllRunStatus() {
  const { getInstanceStats } = require('./engine-manager');
  
  return {
    stats: getInstanceStats(),
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  runByPort,
  stopByPort,
  getRunStatus,
  getAllRunStatus
};
