const crypto = require('crypto');

/**
 * HTTP加密服务
 * 提供AES-256-CBC加密和解密功能
 * 完全按照原项目实现
 */
class HttpCryptoService {
  static MARKER = '@';
  static SALT_LENGTH = 8;
  static SECRET_KEY = 'dY@pP$3cuR1tY';
  
  /**
   * 加密数据 - 完全按照原项目实现
   * @param {string|Object} data 要加密的数据
   * @returns {string} 加密后的数据
   */
  static encrypt(data) {
    try {
      const text = typeof data === 'string' ? data : JSON.stringify(data);
      const salt = this.generateSalt(this.SALT_LENGTH);
      const key = this.deriveKey(this.SECRET_KEY, salt);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      const ivBase64 = iv.toString('base64');
      return `${this.MARKER}${salt}${ivBase64}${encrypted}`;
    } catch (error) {
      console.error('加密失败:', error);
      throw new Error('加密失败');
    }
  }
  
  /**
   * 解密数据 - 完全按照原项目实现
   * @param {string} encryptedData 加密的数据
   * @returns {string|Object} 解密后的数据
   */
  static decrypt(encryptedData) {
    try {
      if (!encryptedData || !encryptedData.startsWith(this.MARKER)) {
        throw new Error('无效的加密数据');
      }

      const salt = encryptedData.substring(1, this.SALT_LENGTH + 1);
      const ivBase64 = encryptedData.substring(this.SALT_LENGTH + 1, this.SALT_LENGTH + 25);
      const encrypted = encryptedData.substring(this.SALT_LENGTH + 25);

      const key = this.deriveKey(this.SECRET_KEY, salt);
      const iv = Buffer.from(ivBase64, 'base64');
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

      let decrypted = decipher.update(encrypted, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      try {
        return JSON.parse(decrypted);
      } catch (error) {
        return decrypted;
      }
    } catch (error) {
      console.error('解密失败:', error);
      throw new Error('解密失败');
    }
  }
  
  /**
   * 密钥派生函数 - 完全按照原项目实现
   * @param {string} secretKey 密钥
   * @param {string} salt 盐值
   * @returns {Buffer} 派生的密钥
   */
  static deriveKey(secretKey, salt) {
    return crypto.pbkdf2Sync(secretKey, salt, 1000, 32, 'sha256');
  }

  /**
   * 生成盐值 - 完全按照原项目实现
   * @param {number} length 长度
   * @returns {string} 盐值
   */
  static generateSalt(length) {
    let salt = '';
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < length; i++) {
      salt += chars.charAt(Math.floor(Math.random() * 62));
    }
    return salt;
  }

  /**
   * 检查是否为加密数据
   * @param {string} data 数据
   * @returns {boolean} 是否为加密数据
   */
  static isEncrypted(data) {
    return typeof data === 'string' && data.startsWith(this.MARKER);
  }
}

module.exports = { HttpCryptoService };
