const { EventEmitter } = require('events');
const axios = require('axios');

/**
 * DiggMonitor - 完全按照原项目实现
 * 抖音点赞监控器
 */
class DiggMonitor {
  constructor(port, headless = true) {
    this.port = port;
    this.headless = headless;
    this.eventEmitter = new EventEmitter();
    this.startTag = false;
    this.finishTag = false;
    this.allowRepeat = false;
    this.monitorTimeout = 30000;
    this.waitPromise = null;
    this.waitResolve = null;
    this.waitReject = null;
    this.engine = null;
    this.page = null;
    this.interceptedRequest = null;
  }
  
  /**
   * 设置监控超时时间
   * @param {number} timeout 超时时间（毫秒）
   */
  setMonitorTimeout(timeout) {
    this.monitorTimeout = timeout;
    return this;
  }
  
  /**
   * 设置是否允许重复
   * @param {boolean} allow 是否允许重复
   */
  setAllowRepeat(allow) {
    this.allowRepeat = allow;
    return this;
  }
  
  /**
   * 获取事件键
   */
  getEventKey() {
    return `digg_monitor_${this.port}`;
  }
  
  /**
   * 启动监控器
   */
  async start() {
    if (this.startTag) return;
    
    this.startTag = true;
    this.finishTag = false;
    
    // 创建等待Promise
    this.waitPromise = new Promise((resolve, reject) => {
      this.waitResolve = resolve;
      this.waitReject = reject;
    });
    
    // 设置超时
    setTimeout(() => {
      if (!this.allowRepeat && !this.finishTag) {
        this.finishTag = true;
        this.waitReject && this.waitReject(new Error('监控超时'));
      }
    }, this.monitorTimeout);
    
    // 监听事件
    this.eventEmitter.once(this.getEventKey(), async (response) => {
      try {
        await this.waitResolve(response);
      } catch (error) {
        console.error('处理监控事件时出错:', error);
        this.waitReject && this.waitReject(error);
      }
    });
    
    return this.waitPromise;
  }
  
  /**
   * 等待操作完成
   */
  async waitForAction() {
    return await this.waitPromise;
  }
  
  /**
   * 匹配响应
   * @param {Response} response Playwright响应对象
   */
  async doMatchResponse(response) {
    const url = response.url();
    const method = response.request().method();
    
    // 匹配点赞API请求
    if (method === 'POST' && url.includes('/web/aweme/digg/')) {
      console.log('检测到点赞API请求:', url);
      return true;
    }
    
    return false;
  }
  
  /**
   * 过滤请求
   * @param {Request} request Playwright请求对象
   * @param {Object} headers 请求头
   * @param {string} postData 请求体
   * @param {Object} options 选项
   */
  async filter(request, headers, postData, options) {
    return false; // 默认不过滤
  }
  
  /**
   * 启动点赞监控 - 完全按照原项目实现
   * @param {DoorEngine} engine 引擎实例
   */
  async start(engine) {
    this.engine = engine;
    this.page = engine.getPage();

    if (!this.page) {
      throw new Error('页面未初始化');
    }

    // 导航到抖音推荐页
    await this.page.goto('https://www.douyin.com/?recommend=1');
    await this.page.waitForTimeout(3000);

    // 添加到引擎的监控器列表
    engine.addMonitor(this);

    console.log(`DiggMonitor已启动，端口: ${this.port}`);
    return this;
  }
  
  /**
   * 执行点赞操作 - 完全按照原项目实现
   * @param {string} awemeId 视频ID
   * @param {Object} options 选项
   */
  async diggClick(awemeId, options = {}) {
    if (!this.page) {
      throw new Error('页面未初始化');
    }

    try {
      // 设置拦截器
      await this.diggIntercept(awemeId, options);

      // 查找并点击点赞按钮
      const diggButton = await this.page.locator('[data-e2e="digg-icon"], .digg-icon, .like-icon').first();

      if (await diggButton.isVisible()) {
        await diggButton.click();
        console.log(`DiggMonitor点赞操作已执行，视频ID: ${awemeId}`);
        return { success: true, awemeId };
      } else {
        // 如果找不到按钮，尝试其他选择器
        const alternativeButton = await this.page.locator('xg-icon[name="like"]').first();
        if (await alternativeButton.isVisible()) {
          await alternativeButton.click();
          console.log(`DiggMonitor点赞操作已执行（备用按钮），视频ID: ${awemeId}`);
          return { success: true, awemeId };
        } else {
          throw new Error('未找到点赞按钮');
        }
      }
    } catch (error) {
      console.error('DiggMonitor点赞操作失败:', error);
      throw error;
    }
  }
  
  /**
   * 拦截并修改点赞请求 - 完全按照原项目实现
   * @param {string} targetAwemeId 目标视频ID
   * @param {Object} options 选项
   */
  async diggIntercept(targetAwemeId, options = {}) {
    if (!this.page) {
      throw new Error('页面未初始化');
    }

    // 移除之前的拦截器
    await this.page.unroute('**/web/aweme/digg/**');

    // 设置新的拦截器
    await this.page.route('**/web/aweme/digg/**', async (route) => {
      const request = route.request();
      const url = request.url();
      const method = request.method();

      if (method === 'POST') {
        try {
          // 获取原始请求数据
          const postData = request.postData();
          const headers = await request.allHeaders();

          console.log(`DiggMonitor拦截到点赞请求: ${url}`);
          console.log(`原始aweme_id: ${this.extractAwemeId(postData)}`);

          // 解析请求参数
          const urlParams = new URLSearchParams(postData);

          // 修改aweme_id为目标ID
          urlParams.set('aweme_id', targetAwemeId);

          // 构造新的请求
          const newPostData = urlParams.toString();

          console.log(`修改后的aweme_id: ${targetAwemeId}`);

          // 使用axios发送修改后的请求
          const response = await axios.post(url, newPostData, {
            headers: {
              ...headers,
              'Content-Type': 'application/x-www-form-urlencoded',
              'Content-Length': newPostData.length.toString()
            },
            timeout: 10000
          });

          // 返回响应
          await route.fulfill({
            status: response.status,
            headers: response.headers,
            body: JSON.stringify(response.data)
          });

          console.log(`DiggMonitor点赞请求拦截成功，目标视频ID: ${targetAwemeId}`);
        } catch (error) {
          console.error('DiggMonitor拦截点赞请求时出错:', error);
          await route.continue();
        }
      } else {
        await route.continue();
      }
    });
  }

  /**
   * 从请求数据中提取aweme_id
   * @param {string} postData 请求数据
   * @returns {string} aweme_id
   */
  extractAwemeId(postData) {
    try {
      const urlParams = new URLSearchParams(postData);
      return urlParams.get('aweme_id') || '';
    } catch (error) {
      return '';
    }
  }
  
  /**
   * 停止监控
   */
  stop() {
    this.finishTag = true;
    this.startTag = false;
    this.eventEmitter.removeAllListeners();
  }
  
  /**
   * 重置监控器状态
   */
  reset() {
    this.startTag = false;
    this.finishTag = false;
    this.waitPromise = null;
    this.waitResolve = null;
    this.waitReject = null;
  }
}

module.exports = { DiggMonitor };
