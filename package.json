{"name": "dydz-electron", "version": "1.0.0", "description": "抖音点赞软件 - 基于Electron和Playwright的自动化工具", "main": "src/main.js", "author": "", "license": "ISC", "scripts": {"start": "electron .", "dev": "electron . --dev", "pack": "electron-builder", "dist": "electron-builder"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"@types/semver": "^7.5.8", "adm-zip": "^0.5.14", "asynckit": "^0.4.0", "axios": "^1.7.2", "builder-util-runtime": "^9.2.10", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "combined-stream": "^1.0.8", "cors": "^2.8.5", "crypto": "1.0.1", "debug": "^4.4.0", "detect-libc": "^2.0.3", "dotenv": "^16.4.5", "electron-log": "^5.2.4", "electron-store": "8.1.0", "express": "^4.21.2", "extract-zip": "^2.0.1", "follow-redirects": "^1.15.9", "form-data": "^4.0.1", "fs-extra": "^11.2.0", "graceful-fs": "^4.2.11", "https-proxy-agent": "^7.0.6", "js-yaml": "^4.1.0", "jsonfile": "^6.1.0", "lazy-val": "^1.0.5", "localforage": "^1.10.0", "lodash.escaperegexp": "^4.1.2", "lodash.isequal": "^4.5.0", "mime-db": "^1.53.0", "mime-types": "^2.1.35", "module-alias": "^2.2.3", "ms": "^2.1.3", "platform-specific": "^1.1.0", "playwright": "^1.49.1", "playwright-core": "^1.49.1", "proxy-from-env": "^1.1.0", "reflect-metadata": "^0.2.2", "sax": "^1.4.1", "semver": "^7.6.3", "sharp": "^0.34.3", "typed-emitter": "^2.1.0", "universalify": "^2.0.1", "url": "^0.11.3", "uuid": "^11.0.3", "xlsx": "0.18.5"}}