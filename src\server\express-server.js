const express = require('express');
const cors = require('cors');
const { HttpCryptoService } = require('../security/crypto-service');

/**
 * SecureExpressServer - 完全按照原项目实现
 */
class SecureExpressServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.isRunning = false;
    this.port = 23333;
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // 基础中间件
    this.app.use(cors());

    // 自定义body解析器，处理加密数据
    this.app.use((req, res, next) => {
      if (req.method === 'POST' && req.headers['content-type'] === 'application/json') {
        let body = '';

        req.on('data', chunk => {
          body += chunk.toString();
        });

        req.on('end', () => {
          try {
            // 移除可能的引号
            const cleanBody = body.replace(/^"|"$/g, '');

            // 检查是否为加密数据
            if (cleanBody && HttpCryptoService.isEncrypted(cleanBody)) {
              const decryptedData = HttpCryptoService.decrypt(cleanBody);
              req.body = decryptedData;
              console.log('Request body decrypted successfully');
            } else {
              // 如果不是加密数据，尝试解析为JSON
              req.body = body ? JSON.parse(body) : {};
            }

            next();
          } catch (error) {
            console.error('Request body processing failed:', error);
            return res.status(400).json({ error: 'Invalid request data format' });
          }
        });
      } else {
        next();
      }
    });

    // 响应加密中间件
    this.app.use((req, res, next) => {
      const originalJson = res.json;

      res.json = function(data) {
        try {
          const jsonString = JSON.stringify(data);
          const encryptedData = HttpCryptoService.encrypt(jsonString);

          res.setHeader('Content-Type', 'application/json');
          res.setHeader('X-Encrypted', 'true');

          console.log('JSON response data encrypted successfully');
          return res.send(encryptedData);
        } catch (error) {
          console.error('JSON response data encryption failed:', error);
          return originalJson.call(this, { error: 'Response data processing failed' });
        }
      };

      next();
    });
  }

  setupRoutes() {
    // 健康检查接口
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        port: this.port,
        secure: true
      });
    });

    // 登录相关API
    this.app.post('/api/login/await-result', this.handleLoginAwaitResult.bind(this));
    this.app.post('/api/login/await-qr-result', this.handleLoginAwaitQRResult.bind(this));
    this.app.post('/api/login/sms-login', this.handleSMSLogin.bind(this));
    this.app.post('/api/login/get-validate-code', this.handleGetValidateCode.bind(this));

    // 运行相关API
    this.app.post('/api/run/start', this.handleRunStart.bind(this));
    this.app.post('/api/run/digg', this.handleRunDigg.bind(this));
    this.app.post('/api/run/stop', this.handleRunStop.bind(this));

    // 错误处理中间件
    this.app.use(this.errorHandler.bind(this));
  }

  // 登录相关处理器
  async handleLoginAwaitResult(req, res) {
    try {
      const { port, headless = true } = req.body;
      const { awaitByLoginResult } = require('../douyin/login-service');
      const result = await awaitByLoginResult(port, headless);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleLoginAwaitQRResult(req, res) {
    try {
      const { port } = req.body;
      const { awaitByLoginResultByQR } = require('../douyin/login-service');
      const result = await awaitByLoginResultByQR(port);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleSMSLogin(req, res) {
    try {
      const { port, phone, code } = req.body;
      const { loginByPhone } = require('../douyin/login-service');
      const result = await loginByPhone(port, phone, code);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleGetValidateCode(req, res) {
    try {
      const { port, phone } = req.body;
      const { getValidateCodeByPhone } = require('../douyin/login-service');
      const result = await getValidateCodeByPhone(port, phone);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // 运行相关处理器
  async handleRunStart(req, res) {
    try {
      const { port, headless = true } = req.body;
      const { runByPort } = require('../douyin/run-service');
      const result = await runByPort(port, headless);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleRunDigg(req, res) {
    try {
      const { port, awemeId, options = {} } = req.body;
      const { diggByPort } = require('../douyin/digg-service');
      const result = await diggByPort(port, awemeId, options);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleRunStop(req, res) {
    try {
      const { port, headless = true } = req.body;
      const { stopByPort } = require('../douyin/run-service');
      const result = await stopByPort(port, headless);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // 错误处理器
  errorHandler(error, req, res, next) {
    console.error('Express server error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }

  // 启动服务器
  start(port = 23333) {
    return new Promise((resolve, reject) => {
      this.port = port;
      this.server = this.app.listen(port, (error) => {
        if (error) {
          console.error('SecureExpressServer启动失败:', error);
          reject(error);
        } else {
          this.isRunning = true;
          console.log(`SecureExpressServer started successfully on port ${port}`);
          resolve();
        }
      });
    });
  }

  // 停止服务器
  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.isRunning = false;
          this.server = null;
          console.log('SecureExpressServer stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // 获取状态
  getStatus() {
    return {
      running: this.isRunning,
      port: this.port
    };
  }
}

// 全局实例
let secureServer = null;

/**
 * 启动Express服务器 - 使用SecureExpressServer
 * @param {number} port 端口号，默认23333
 * @param {boolean} useSecureMode 是否使用加密模式
 */
async function startExpressServer(port = 23333, useSecureMode = true) {
  if (secureServer && secureServer.isRunning) {
    console.log('SecureExpressServer已在运行');
    return;
  }

  secureServer = new SecureExpressServer();
  await secureServer.start(port);
}

/**
 * 停止Express服务器
 */
async function stopExpressServer() {
  if (secureServer) {
    await secureServer.stop();
    secureServer = null;
  }
}

/**
 * 获取服务器状态
 */
function getServerStatus() {
  return secureServer ? secureServer.getStatus() : { running: false, port: null };
}

module.exports = {
  startExpressServer,
  stopExpressServer,
  getServerStatus
};
