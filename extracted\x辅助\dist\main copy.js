(() => {
    "use strict";
    var e = {
            181: (e, t, n) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.init = function(e) {
                        (0, o.initStore)(e);
                    }));
                const o = n(1969);
            },
            647: function(e, t, n) {
                var o,
                    i =
                    (this && this.__createBinding) ||
                    (Object.create ?
                        function(e, t, n, o) {
                            void 0 === o && (o = n);
                            var i = Object.getOwnPropertyDescriptor(t, n);
                            ((i &&
                                    !("get" in i ?
                                        !t.__esModule :
                                        i.writable || i.configurable)) ||
                                (i = {
                                    enumerable: !0,
                                    get: function() {
                                        return t[n];
                                    },
                                }),
                                Object.defineProperty(e, o, i));
                        } :
                        function(e, t, n, o) {
                            (void 0 === o && (o = n), (e[o] = t[n]));
                        }),
                    r =
                    (this && this.__setModuleDefault) ||
                    (Object.create ?
                        function(e, t) {
                            Object.defineProperty(e, "default", {
                                enumerable: !0,
                                value: t,
                            });
                        } :
                        function(e, t) {
                            e.default = t;
                        }),
                    s =
                    (this && this.__importStar) ||
                    ((o = function(e) {
                            return (
                                (o =
                                    Object.getOwnPropertyNames ||
                                    function(e) {
                                        var t = [];
                                        for (var n in e)
                                            Object.prototype.hasOwnProperty.call(e, n) &&
                                            (t[t.length] = n);
                                        return t;
                                    }),
                                o(e)
                            );
                        }),
                        function(e) {
                            if (e && e.__esModule) return e;
                            var t = {};
                            if (null != e)
                                for (var n = o(e), s = 0; s < n.length; s++)
                                    "default" !== n[s] && i(t, e, n[s]);
                            return (r(t, e), t);
                        }),
                    a =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    c =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.registerRpc = function() {
                        return a(this, void 0, void 0, function*() {
                            (0, l.registerApiImpl)().forEach((e) => {
                                !(function(e) {
                                    const t = e.prototype;
                                    Object.getOwnPropertyNames(t)
                                        .filter((e) => "constructor" !== e)
                                        .forEach((o) =>
                                            a(this, void 0, void 0, function*() {
                                                const i = t[o],
                                                    r = new e(),
                                                    c = Reflect.getMetadata("invokeType", t, o);
                                                if (c == u.Protocols.INVOKE) {
                                                    const t = r.getNamespace(),
                                                        u = r.getApiName();
                                                    let l = u;
                                                    (t && (l = t + "_" + u),
                                                        f.default.info("metadata impl", c, `${l}.${o}`),
                                                        d.ipcMain.handle(`${l}.${o}`, (t, ...o) =>
                                                            a(this, void 0, void 0, function*() {
                                                                const r = new e(),
                                                                    a = t.sender,
                                                                    c = a.port || 0,
                                                                    u = a.windowId || "main";
                                                                if (
                                                                    (r.setPort(c), r.setWindowId(u), c && 0 !== c)
                                                                ) {
                                                                    const {
                                                                        PortManager: e
                                                                    } =
                                                                    yield Promise.resolve().then(() =>
                                                                            s(n(4242)),
                                                                        ),
                                                                        t = e.getInstance().getInstance(c);
                                                                    t
                                                                        ?
                                                                        r.setWindows(t.window) :
                                                                        r.setWindows(h.mainWindow);
                                                                } else r.setWindows(h.mainWindow);
                                                                return i.apply(r, o);
                                                            }),
                                                        ));
                                                }
                                            }),
                                        );
                                })(e);
                            });
                        });
                    }));
                const u = n(4759),
                    l = n(5898),
                    d = n(4482),
                    f = c(n(1181)),
                    h = n(5857);
            },
            818: (e) => {
                e.exports = require("dotenv");
            },
            835: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DoorEngine = void 0),
                    (t.getSecChUa = m),
                    (t.initPlatform = function() {
                        return o(this, void 0, void 0, function*() {
                            let e;
                            try {
                                let t = yield b();
                                if (t) return t;
                                let n = yield v();
                                e = yield a.chromium.launch({
                                    headless: !1,
                                    executablePath: n,
                                    args: [
                                        "--disable-accelerated-2d-canvas",
                                        "--disable-webgl",
                                        "--disable-software-rasterizer",
                                        "--no-sandbox",
                                        "--disable-setuid-sandbox",
                                        "--disable-blink-features=AutomationControlled",
                                    ],
                                });
                                const o = yield e.newContext(),
                                    i = yield o.newPage();
                                return (
                                    yield i.goto("https://www.baidu.com"),
                                        (t = yield w(i)),
                                        f.default.info("login platform is ", JSON.stringify(t)),
                                        t
                                );
                            } catch (e) {
                                f.default.error("initPlatform error", e);
                            } finally {
                                e && (yield e.close());
                            }
                        });
                    }),
                    (t.setPlatform = w),
                    (t.getPlatform = b));
                const r = i(n(6928)),
                    s = i(n(9896)),
                    a = n(5883),
                    c = n(1969),
                    u = n(4482),
                    l = n(8922),
                    d = n(3466),
                    f = i(n(1181)),
                    h = i(n(857)),
                    p = n(1643),
                    g = new Map(),
                    y = new Map();

                function v() {
                    if (process.env.CHROME_PATH) {
                        const e = process.env.CHROME_PATH;
                        if (
                            (console.log(`使用环境变量中的Chrome路径: ${e}`),
                                s.default.existsSync(e))
                        )
                            return (console.log(`✅ 环境变量路径有效: ${e}`), e);
                        (console.log(`❌ 环境变量路径无效: ${e}`),
                            console.log("将尝试自动检测系统Chrome路径..."));
                    }
                    try {
                        return (function() {
                            const e = h.default.platform();
                            switch ((console.log(`检测操作系统: ${e}`), e)) {
                                case "darwin":
                                    const t = [
                                        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                                        "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta",
                                        "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev",
                                        "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
                                    ];
                                    console.log("检测macOS Chrome路径...");
                                    for (const e of t)
                                        if (
                                            (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                        )
                                            return (console.log(`✅ 找到Chrome: ${e}`), e);
                                    break;
                                case "win32":
                                    const n = [
                                        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                                        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                                        r.default.join(
                                            h.default.homedir(),
                                            "AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
                                        ),
                                        r.default.join(
                                            h.default.homedir(),
                                            "AppData\\Local\\Google\\Chrome Beta\\Application\\chrome.exe",
                                        ),
                                        r.default.join(
                                            h.default.homedir(),
                                            "AppData\\Local\\Google\\Chrome Dev\\Application\\chrome.exe",
                                        ),
                                        r.default.join(
                                            h.default.homedir(),
                                            "AppData\\Local\\Google\\Chrome SxS\\Application\\chrome.exe",
                                        ),
                                        "C:\\Program Files\\Google\\Chrome Beta\\Application\\chrome.exe",
                                        "C:\\Program Files (x86)\\Google\\Chrome Beta\\Application\\chrome.exe",
                                    ];
                                    console.log("检测Windows Chrome路径...");
                                    for (const e of n)
                                        if (
                                            (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                        )
                                            return (console.log(`✅ 找到Chrome: ${e}`), e);
                                    break;
                                case "linux":
                                    const o = [
                                        "/usr/bin/google-chrome",
                                        "/usr/bin/google-chrome-stable",
                                        "/usr/bin/google-chrome-beta",
                                        "/usr/bin/google-chrome-unstable",
                                        "/usr/bin/chromium-browser",
                                        "/usr/bin/chromium",
                                        "/snap/bin/chromium",
                                        "/var/lib/snapd/snap/bin/chromium",
                                        "/usr/local/bin/google-chrome",
                                    ];
                                    console.log("检测Linux Chrome路径...");
                                    for (const e of o)
                                        if (
                                            (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                        )
                                            return (console.log(`✅ 找到Chrome: ${e}`), e);
                                    break;
                                default:
                                    throw new Error(`不支持的操作系统: ${e}`);
                            }
                            throw new Error(
                                `未找到系统安装的Chrome浏览器，请检查Chrome是否已安装。操作系统: ${e}`,
                            );
                        })();
                    } catch (e) {
                        throw (console.error("❌ Chrome路径检测失败:", e.message), e);
                    }
                }

                function m(e) {
                    if (!e) return "";
                    const t = e.userAgentData.brands,
                        n = [];
                    for (const e of t) n.push(`"${e.brand}";v="${e.version}"`);
                    return n.join(", ");
                }

                function w(e) {
                    return o(this, void 0, void 0, function*() {
                        const t = yield e.evaluate(() => {
                            const e = navigator,
                                t = {};
                            for (let n in e) t[n] = e[n];
                            return t;
                        });
                        return (
                            (0, c.set)(
                                "browserPlatform_" + (process.env.CHROME_VERSION || "1169"),
                                JSON.stringify(t),
                            ),
                            t
                        );
                    });
                }

                function b() {
                    return o(this, void 0, void 0, function*() {
                        const e = process.env.CHROME_VERSION || "1169",
                            t = yield(0, c.get)("browserPlatform_" + e);
                        if (t) return JSON.parse(t);
                    });
                }
                t.DoorEngine = class {
                    constructor(e, t = !0, n = "", o = !1, i = void 0) {
                        ((this.headless = !0),
                            (this.monitors = []),
                            (this.monitorsChain = []),
                            (this.needValidateImage = !1),
                            (this.browserArgs = [
                                "--disable-accelerated-2d-canvas",
                                "--disable-webgl",
                                "--disable-software-rasterizer",
                                "--no-sandbox",
                                "--disable-setuid-sandbox",
                                "--disable-webrtc-encryption",
                                "--disable-webrtc-hw-decoding",
                                "--disable-webrtc-hw-encoding",
                                "--disable-extensions-file-access-check",
                                "--disable-blink-features=AutomationControlled",
                                "--disable-background-timer-throttling",
                                "--disable-renderer-backgrounding",
                                "--disable-backgrounding-occluded-windows",
                                "--disable-dev-shm-usage",
                                "--disable-gpu-sandbox",
                                "--no-first-run",
                                "--no-default-browser-check",
                                "--disable-default-apps",
                                "--disable-features=TranslateUI",
                            ]),
                            (this.resourceId = e),
                            (this.usePersistentContext = o),
                            (this.chromePath = n || this.getChromePath()),
                            (this.headless = t),
                            i && (this.browserArgs = i));
                        try {
                            const e = u.screen.getPrimaryDisplay();
                            ((this.width = e.workAreaSize.width),
                                (this.height = e.workAreaSize.height));
                        } catch (e) {
                            ((this.width = 1920),
                                (this.height = 1080),
                                f.default.error("init width and height error", e));
                        }
                    }
                    setNeedValidateImage(e) {
                        this.needValidateImage = e;
                    }
                    getChromePath() {
                        return process.env.CHROME_PATH;
                    }
                    addMonitor(e) {
                        this.monitors.push(e);
                    }
                    getPage() {
                        return this.page;
                    }
                    addMonitorChain(e) {
                        (this.monitorsChain.push(e),
                            this.monitors.push(...e.getMonitors()));
                    }
                    init() {
                        return o(this, arguments, void 0, function*(e = void 0) {
                            if (
                                ((this.browser = yield this.createBrowser()),
                                    this.context || (this.context = yield this.createContext()),
                                    !this.context)
                            )
                                return void f.default.info("context is null");
                            const t = yield this.context.newPage();
                            return (
                                yield t.setViewportSize({
                                        width: this.width,
                                        height: this.height,
                                    }),
                                    e && (yield t.goto(e)),
                                    this.onRequest(t),
                                    this.onResponse(t),
                                    (this.page = t),
                                    t
                            );
                        });
                    }
                    initByPersistentContext() {
                        return o(this, arguments, void 0, function*(e = void 0) {
                            if (
                                ((this.context = yield this.createContextByPersistentContext()),
                                    !this.context)
                            )
                                return;
                            const t = yield this.context.newPage();
                            return (
                                yield t.setViewportSize({
                                        width: this.width,
                                        height: this.height,
                                    }),
                                    e && (yield t.goto(e)),
                                    this.onRequest(t),
                                    this.onResponse(t),
                                    (this.page = t),
                                    t
                            );
                        });
                    }
                    createContextByPersistentContext() {
                        return o(this, void 0, void 0, function*() {
                            let e = yield this.getRealChromePath(),
                                t = this.getKey();
                            if (
                                (e && (t += "_" + e),
                                    f.default.info("browser key is ", t),
                                    y.has(t))
                            )
                                return y.get(t);
                            const n = this.getUserDataDir();
                            f.default.info("userDataDir is ", n);
                            const o = yield b(),
                                i = {
                                    headless: this.headless,
                                    executablePath: e,
                                    args: [
                                        "--disable-accelerated-2d-canvas",
                                        "--disable-webgl",
                                        "--disable-software-rasterizer",
                                        "--no-sandbox",
                                        "--disable-setuid-sandbox",
                                        "--disable-blink-features=AutomationControlled",
                                        "--window-size=" + this.width + "," + this.height,
                                    ],
                                    extraHTTPHeaders: {
                                        "sec-ch-ua": m(o),
                                        "sec-ch-ua-mobile": "?0",
                                        "sec-ch-ua-platform": `"${o.userAgentData.platform}"`,
                                    },
                                    userAgent: o.userAgent,
                                    bypassCSP: !0,
                                    locale: "zh-CN",
                                };
                            try {
                                const e = p.ProxyService.getInstance(),
                                    t = yield e.getPlaywrightProxyConfig(this.resourceId);
                                t
                                    ?
                                    (f.default.info(
                                            `[Engine] 为持久化浏览器上下文应用代理配置: ${JSON.stringify(t)}`,
                                        ),
                                        (i.proxy = t)) :
                                    f.default.info("[Engine] 持久化上下文未使用代理配置");
                            } catch (e) {
                                f.default.error("[Engine] 应用持久化上下文代理配置失败:", e);
                            }
                            const r = yield a.chromium.launchPersistentContext(n, i);
                            return (y.set(t, r), r);
                        });
                    }
                    getContext() {
                        return this.context;
                    }
                    closePage() {
                        return o(this, void 0, void 0, function*() {
                            this.page && (yield this.page.close());
                        });
                    }
                    release() {
                        return o(this, void 0, void 0, function*() {});
                    }
                    doBeforeRequest(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            let o = !1;
                            for (const i of this.monitors) {
                                if (yield i.filter(t.url(), t.resourceType(), t.method(), n)) {
                                    (yield e.abort(), (o = !0));
                                    continue;
                                }
                                if (i.finishTag) continue;
                                if (!(i instanceof l.MonitorRequest)) continue;
                                if (!(yield i.isMatch(t.url(), t.method(), n))) continue;
                                const r = i;
                                let s;
                                r.handler && (s = yield r.handler(t, void 0));
                                let a = {};
                                r.needHeaderData() && (a = yield t.allHeaders());
                                let c = "";
                                r.needUrl() && (c = t.url());
                                let u = {};
                                if (r.needRequestBody()) {
                                    const e = t.postData();
                                    if (e) {
                                        const t = new URLSearchParams(e);
                                        u = Object.fromEntries(t.entries());
                                    }
                                }
                                (i._doCallback(new d.DoorEntity(!!s, s, c, a, u)),
                                    i.setFinishTag(!0));
                            }
                            return o;
                        });
                    }
                    onRequest(e) {
                        return o(this, void 0, void 0, function*() {
                            e.route("*/**", (e) =>
                                o(this, void 0, void 0, function*() {
                                    const t = e.request(),
                                        n = yield t.allHeaders();
                                    (yield this.doBeforeRequest(e, t, n)) || e.continue();
                                }),
                            );
                        });
                    }
                    doAfterResponse(e) {
                        return o(this, void 0, void 0, function*() {
                            for (const t of this.monitors) {
                                if (t.finishTag) continue;
                                if (!(t instanceof l.MonitorResponse)) continue;
                                const n = t;
                                if (!(yield t.doMatchResponse(e))) continue;
                                let o = {};
                                const i = e.request(),
                                    r = yield i.allHeaders();
                                n.needHeaderData() && (o = r);
                                let s = "";
                                n.needUrl() && (s = i.url());
                                let a = {};
                                n.needResponseHeaderData() && (a = yield e.allHeaders());
                                let c = {};
                                if (n.needRequestBody()) {
                                    const e = i.postData();
                                    if (e) {
                                        const t = new URLSearchParams(e);
                                        c = Object.fromEntries(t.entries());
                                    }
                                }
                                const u = yield n.getResponseData(e);
                                ((u.url = s),
                                    (u.headerData = o),
                                    (u.requestBody = c),
                                    (u.responseHeaderData = a),
                                    n._doCallback(u, e.request(), e),
                                    n.setFinishTag(!0));
                            }
                        });
                    }
                    onResponse(e) {
                        return o(this, void 0, void 0, function*() {
                            e.on("response", (e) =>
                                o(this, void 0, void 0, function*() {
                                    yield this.doAfterResponse(e);
                                }),
                            );
                        });
                    }
                    resetMonitor() {
                        ((this.monitors = []), (this.monitorsChain = []));
                    }
                    resetListener(e) {
                        (this.onRequest(e), this.onResponse(e));
                    }
                    openWaitMonitor(e, t, n) {
                        return o(
                            this,
                            arguments,
                            void 0,
                            function*(
                                e,
                                t,
                                n,
                                i = {},
                                r = (e, ...t) => o(this, void 0, void 0, function*() {}),
                                ...s
                            ) {
                                (this.addMonitor(n),
                                    yield this.startMonitor(),
                                        t && (yield e.goto(t)));
                                const a = yield r(e, ...s);
                                return null != a ? (d.DoorEntity, a) : yield n.waitForAction();
                            },
                        );
                    }
                    openNotWaitMonitor(e, t, n) {
                        return o(
                            this,
                            arguments,
                            void 0,
                            function*(e, t, n, o = {}, i, ...r) {
                                return (
                                    this.addMonitor(n),
                                    yield this.startMonitor(),
                                        yield e.goto(t),
                                            yield i(e, ...r)
                                );
                            },
                        );
                    }
                    openWaitMonitorChain(e, t, n) {
                        return o(
                            this,
                            arguments,
                            void 0,
                            function*(
                                e,
                                t,
                                n,
                                i = {},
                                r = (e, ...t) => o(this, void 0, void 0, function*() {}),
                                ...s
                            ) {
                                return (
                                    n.getItemKeys(t),
                                    this.addMonitorChain(n),
                                    yield this.startMonitor(),
                                        yield e.goto(t),
                                            yield r(e, ...s),
                                                yield n.waitForAction()
                                );
                            },
                        );
                    }
                    startMonitor() {
                        return o(this, void 0, void 0, function*() {
                            for (const e of this.monitors) e.start();
                        });
                    }
                    getMonitorChainFromChain(e) {
                        if (this.monitorsChain && 0 != this.monitorsChain.length)
                            for (const t of this.monitorsChain)
                                if (t.getKey() == e) return t;
                    }
                    getMonitor(e) {
                        if (this.monitors && 0 != this.monitors.length)
                            for (const t of this.monitors)
                                if (t.getKey() == e) return t;
                    }
                    closeContext() {
                        return o(this, void 0, void 0, function*() {
                            this.context && (yield this.context.close());
                        });
                    }
                    closeBrowser() {
                        return o(this, void 0, void 0, function*() {
                            this.browser && (yield this.browser.close());
                        });
                    }
                    getKey() {
                        return `door_engine_${this.getNamespace()}_${this.resourceId}`;
                    }
                    getSessionPath() {
                        return o(this, void 0, void 0, function*() {
                            let e = (0, c.get)(this.getKey());
                            if (s.default.existsSync(e)) return e;
                        });
                    }
                    getSessionDir() {
                        const e = Date.now().toString() + ".json",
                            t = (this.constructor.name, u.app.getPath("userData")),
                            n = r.default.join(
                                t,
                                "resource",
                                "session",
                                this.getNamespace(),
                                this.resourceId.toString(),
                            );
                        return (
                            s.default.existsSync(n) ||
                            s.default.mkdirSync(n, {
                                recursive: !0
                            }),
                            r.default.join(n, e)
                        );
                    }
                    getUserDataDir() {
                        const e = u.app.getPath("userData"),
                            t = r.default.join(
                                e,
                                "resource",
                                "userDataDir",
                                this.getNamespace(),
                                this.resourceId.toString(),
                            );
                        return (
                            f.default.info("userDataDir is ", t),
                            s.default.existsSync(t) ||
                            s.default.mkdirSync(t, {
                                recursive: !0
                            }),
                            t
                        );
                    }
                    saveContextState() {
                        return o(this, void 0, void 0, function*() {
                            if (!this.context) return;
                            const e = this.getSessionDir();
                            ((0, c.set)(this.getKey(), e),
                                yield this.context.storageState({
                                    path: e
                                }));
                        });
                    }
                    getHeaderKey() {
                        return `${this.resourceId}_door_header_${this.getKey()}`;
                    }
                    getValidateAutoTagKey() {
                        return `${this.resourceId}_door_validate_auto_tag_${this.getKey()}`;
                    }
                    setHeader(e) {
                        if (!e || 0 == Object.keys(e).length) return;
                        const t = this.getHeaderKey();
                        (0, c.set)(t, e);
                    }
                    setValidateAutoTag(e) {
                        const t = this.getValidateAutoTagKey();
                        (0, c.set)(t, e);
                    }
                    getValidateAutoTag() {
                        const e = this.getValidateAutoTagKey(),
                            t = (0, c.get)(e);
                        return null == t || t;
                    }
                    getHeader() {
                        const e = this.getHeaderKey();
                        return (0, c.get)(e);
                    }
                    clearHeader() {
                        const e = this.getHeaderKey();
                        (0, c.remove)(e);
                    }
                    setParams(e, t) {
                        const n = this.getKey() + "_" + e;
                        (0, c.set)(n, t);
                    }
                    getParams(e) {
                        const t = this.getKey() + "_" + e;
                        return (0, c.get)(t);
                    }
                    createContext() {
                        return o(this, void 0, void 0, function*() {
                            var e;
                            if (!this.browser) return;
                            const t = this.headless.toString() + "_" + this.getKey();
                            if (y.has(t)) return y.get(t);
                            const n = yield this.getRealChromePath(),
                                o = yield b(),
                                    i = {
                                        bypassCSP: !0,
                                        locale: "zh-CN"
                                    };
                            (n && (i.executablePath = n),
                                (i.screen = {
                                    width: this.width,
                                    height: this.height
                                }));
                            const r = yield this.getSessionPath();
                            (r && (i.storageState = r),
                                o &&
                                ((i.userAgent = o.userAgent),
                                    (i.extraHTTPHeaders = {
                                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
                                        "sec-ch-ua": m(o),
                                        "sec-ch-ua-mobile": "?0",
                                        "sec-ch-ua-platform": `"${o.userAgentData.platform}"`,
                                    })));
                            try {
                                const e = p.ProxyService.getInstance(),
                                    t = yield e.getPlaywrightProxyConfig(this.resourceId);
                                t
                                    ?
                                    (f.default.info(
                                            `[Engine] 为浏览器上下文应用代理配置: ${JSON.stringify(t)}`,
                                        ),
                                        (i.proxy = t)) :
                                    f.default.info("[Engine] 未使用代理配置");
                            } catch (e) {
                                f.default.error("[Engine] 应用代理配置失败:", e);
                            }
                            const s = yield null === (e = this.browser) || void 0 === e ?
                                void 0 :
                                e.newContext(i);
                            return (
                                s &&
                                (yield s.addInitScript(() => {
                                    Object.defineProperty(navigator, "webdriver", {
                                        get: () => {},
                                    });
                                })),
                                y.set(t, s),
                                s
                            );
                        });
                    }
                    getRealChromePath() {
                        return o(this, void 0, void 0, function*() {
                            return (yield v()) || this.chromePath;
                        });
                    }
                    getBrowserKey() {
                        let e =
                            this.headless.toString() +
                            "_" +
                            this.needValidateImage.toString();
                        return (this.chromePath && (e += "_" + this.chromePath), e);
                    }
                    createBrowser() {
                        return o(this, void 0, void 0, function*() {
                            let e = this.getBrowserKey();
                            f.default.info("browser key is ", e);
                            let t = yield this.getRealChromePath();
                            if (g.has(e)) return g.get(e);
                            const n = this.width || 1280 + Math.floor(200 * Math.random()),
                                o = this.height || 780 + Math.floor(120 * Math.random()),
                                i = [...this.browserArgs, `--window-size=${n},${o}`],
                                r = yield a.chromium.launch({
                                    headless: this.headless,
                                    slowMo: 15 + Math.floor(30 * Math.random()),
                                    executablePath: t,
                                    args: i,
                                });
                            return (g.set(e, r), r);
                        });
                    }
                    setupNetworkInterception(e) {
                        return o(this, void 0, void 0, function*() {
                            yield e.route("**/*", (e) =>
                                o(this, void 0, void 0, function*() {
                                    const t = e.request(),
                                        n = yield t.allHeaders(),
                                            o = Object.assign(Object.assign({}, n), {
                                                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
                                                "sec-ch-ua": '"Chromium";v="124", "Google Chrome";v="124", "Not-A.Brand";v="99"',
                                                "sec-ch-ua-mobile": "?0",
                                                "sec-ch-ua-platform": '"macOS"',
                                                "sec-fetch-dest": "empty",
                                                "sec-fetch-mode": "cors",
                                                "sec-fetch-site": "same-site",
                                            });
                                    if (
                                        t.url().includes("captcha") ||
                                        t.url().includes("verify") ||
                                        t.url().includes("check") ||
                                        t.url().includes("report") ||
                                        t.url().includes("punish") ||
                                        t.url().includes("_____tmd_____")
                                    ) {
                                        (f.default.info(`发现验证相关请求: ${t.url()}`),
                                            f.default.info(`请求方法: ${t.method()}`));
                                        try {
                                            const e = t.postData();
                                            e && f.default.info(`请求数据: ${e}`);
                                        } catch (e) {
                                            f.default.info(`无法获取请求数据: ${e}`);
                                        }
                                    }
                                    try {
                                        yield e.continue({
                                            headers: o
                                        });
                                    } catch (t) {
                                        yield e.continue();
                                    }
                                }),
                            );
                        });
                    }
                    addAntiDetectionScript(e) {
                        return o(this, void 0, void 0, function*() {
                            yield e.addInitScript(() => {
                                const e = () => {
                                        try {
                                            const e = WebGLRenderingContext.prototype.getParameter;
                                            WebGLRenderingContext.prototype.getParameter = function(
                                                t,
                                            ) {
                                                return 37445 === t ?
                                                    "Intel Open Source Technology Center" :
                                                    37446 === t ?
                                                    "Mesa DRI Intel(R) HD Graphics 630 (Kaby Lake GT2)" :
                                                    e.apply(this, [...arguments]);
                                            };
                                        } catch (e) {}
                                    },
                                    t = () => {
                                        try {
                                            const e = HTMLCanvasElement.prototype.getContext;
                                            HTMLCanvasElement.prototype.getContext = function(t) {
                                                const n = arguments[0],
                                                    o = arguments.length > 1 ? arguments[1] : void 0,
                                                    i = e.call(this, n, o);
                                                if ("2d" === t && i) {
                                                    const e = i.fillText;
                                                    i.fillText = function() {
                                                        const t = Array.from(arguments);
                                                        return (
                                                            t.length > 0 &&
                                                            "string" == typeof t[0] &&
                                                            (t[0] = t[0] + " "),
                                                            e.apply(this, t)
                                                        );
                                                    };
                                                    const t = i.getImageData;
                                                    i.getImageData = function() {
                                                        const e = Array.from(arguments),
                                                            n = t.apply(this, e);
                                                        if (n && n.data && n.data.length > 0)
                                                            for (let e = 0; e < 10; e++) {
                                                                const e = Math.floor(
                                                                    Math.random() * n.data.length,
                                                                );
                                                                n.data[e] = 1 ^ n.data[e];
                                                            }
                                                        return n;
                                                    };
                                                }
                                                return i;
                                            };
                                        } catch (e) {
                                            console.log("Canvas指纹修改失败，但继续执行", e);
                                        }
                                    },
                                    n = () => {
                                        (Object.defineProperty(window, "outerWidth", {
                                                get: function() {
                                                    return window.innerWidth;
                                                },
                                            }),
                                            Object.defineProperty(window, "outerHeight", {
                                                get: function() {
                                                    return window.innerHeight;
                                                },
                                            }),
                                            Object.defineProperty(navigator, "plugins", {
                                                get: function() {
                                                    const e = [];
                                                    return (
                                                        e.push({
                                                            name: "Shockwave Flash",
                                                            description: "Shockwave Flash 32.0 r0",
                                                            filename: "internal-flash.plugin",
                                                            version: "32.0.0",
                                                        }, {
                                                            name: "Chrome PDF Plugin",
                                                            description: "Portable Document Format",
                                                            filename: "internal-pdf.plugin",
                                                            version: "1.0",
                                                        }, {
                                                            name: "Chrome PDF Viewer",
                                                            description: "",
                                                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                                                            version: "1.0",
                                                        }, ),
                                                        (e.item = function(e) {
                                                            return this[e];
                                                        }),
                                                        (e.namedItem = function(e) {
                                                            return this.find((t) => t.name === e);
                                                        }),
                                                        (e.refresh = function() {}),
                                                        e
                                                    );
                                                },
                                            }));
                                        const e = Element.prototype.querySelectorAll;
                                        ((Element.prototype.querySelectorAll = function(t) {
                                                return t && t.includes(":target") ?
                                                    document.createElement("div") :
                                                    e.apply(this, [...arguments]);
                                            }),
                                            void 0 === window.Notification &&
                                            (window.Notification = {
                                                permission: "default",
                                                requestPermission: function() {
                                                    return Promise.resolve("default");
                                                },
                                            }),
                                            navigator.connection ||
                                            (navigator.connection = {
                                                downlink: 10 + 5 * Math.random(),
                                                effectiveType: "4g",
                                                onchange: null,
                                                rtt: 50 + 30 * Math.random(),
                                                saveData: !1,
                                            }),
                                            Object.defineProperty(navigator, "userAgent", {
                                                get: function() {
                                                    return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36";
                                                },
                                            }),
                                            void 0 === navigator.mediaDevices &&
                                            (navigator.mediaDevices = {
                                                enumerateDevices: function() {
                                                    return Promise.resolve([{
                                                            kind: "audioinput",
                                                            deviceId: "default",
                                                            groupId: "default",
                                                            label: "",
                                                        },
                                                        {
                                                            kind: "videoinput",
                                                            deviceId: "default",
                                                            groupId: "default",
                                                            label: "",
                                                        },
                                                    ]);
                                                },
                                            }));
                                    },
                                    o = () => {
                                        if (
                                            (Object.defineProperty(performance, "mark", {
                                                    value: function() {
                                                        const e = Array.from(arguments);
                                                        return e.length > 0 &&
                                                            "string" == typeof e[0] &&
                                                            (e[0].includes("finger") ||
                                                                e[0].includes("detect") ||
                                                                e[0].includes("bot")) ?
                                                            null :
                                                            performance.mark.apply(this, e);
                                                    },
                                                }),
                                                window.AudioContext || window.webkitAudioContext)
                                        ) {
                                            const e =
                                                window.AudioContext || window.webkitAudioContext;
                                            window.AudioContext = window.webkitAudioContext =
                                                function() {
                                                    const t = new e(),
                                                        n = t.createAnalyser().getFloatFrequencyData;
                                                    return (
                                                        (t.createAnalyser().getFloatFrequencyData =
                                                            function(e) {
                                                                const t = n.apply(this, [...arguments]);
                                                                if (e && e.length > 0)
                                                                    for (let t = 0; t < e.length; t += 200)
                                                                        e[t] = e[t] + 0.01 * Math.random();
                                                                return t;
                                                            }),
                                                        t
                                                    );
                                                };
                                        }
                                        void 0 === window.speechSynthesis &&
                                            (window.speechSynthesis = {
                                                pending: !1,
                                                speaking: !1,
                                                paused: !1,
                                                onvoiceschanged: null,
                                                getVoices: function() {
                                                    return [];
                                                },
                                                speak: function() {},
                                                cancel: function() {},
                                                pause: function() {},
                                                resume: function() {},
                                            });
                                    },
                                    i = () => {
                                        if (
                                            (Object.defineProperty(screen, "availWidth", {
                                                    get: function() {
                                                        return window.innerWidth;
                                                    },
                                                }),
                                                Object.defineProperty(screen, "availHeight", {
                                                    get: function() {
                                                        return window.innerHeight;
                                                    },
                                                }),
                                                Object.defineProperty(screen, "width", {
                                                    get: function() {
                                                        return window.innerWidth;
                                                    },
                                                }),
                                                Object.defineProperty(screen, "height", {
                                                    get: function() {
                                                        return window.innerHeight;
                                                    },
                                                }),
                                                window.WebGL2RenderingContext)
                                        ) {
                                            const e = WebGL2RenderingContext.prototype.getParameter;
                                            WebGL2RenderingContext.prototype.getParameter = function(
                                                t,
                                            ) {
                                                return 37445 === t ?
                                                    "Intel Open Source Technology Center" :
                                                    37446 === t ?
                                                    "Mesa DRI Intel(R) HD Graphics 630 (Kaby Lake GT2)" :
                                                    e.apply(this, [...arguments]);
                                            };
                                        }
                                        (0 === navigator.plugins.length &&
                                            Object.defineProperty(navigator, "plugins", {
                                                get: function() {
                                                    const e = {
                                                        name: "Chrome PDF Plugin",
                                                        filename: "internal-pdf-viewer",
                                                        description: "Portable Document Format",
                                                    };
                                                    e.__proto__ = MimeType.prototype;
                                                    const t = [e];
                                                    return (
                                                        (t.item = function(e) {
                                                            return this[e];
                                                        }),
                                                        (t.namedItem = function(e) {
                                                            return this[0].name === e ? this[0] : null;
                                                        }),
                                                        (t.refresh = function() {}),
                                                        (t.length = 1),
                                                        t
                                                    );
                                                },
                                            }),
                                            0 === navigator.mimeTypes.length &&
                                            Object.defineProperty(navigator, "mimeTypes", {
                                                get: function() {
                                                    const e = [{
                                                        type: "application/pdf",
                                                        suffixes: "pdf",
                                                        description: "Portable Document Format",
                                                        enabledPlugin: {},
                                                    }, ];
                                                    return (
                                                        (e.item = function(e) {
                                                            return this[e];
                                                        }),
                                                        (e.namedItem = function(e) {
                                                            return this[0].type === e ? this[0] : null;
                                                        }),
                                                        (e.length = 1),
                                                        e
                                                    );
                                                },
                                            }));
                                    };
                                try {
                                    (Object.defineProperty(navigator, "webdriver", {
                                            get: () => !1,
                                        }),
                                        Object.defineProperty(navigator, "languages", {
                                            get: function() {
                                                return ["zh-CN", "zh", "en-US", "en"];
                                            },
                                        }),
                                        Object.defineProperty(navigator, "hardwareConcurrency", {
                                            get: function() {
                                                return 8;
                                            },
                                        }),
                                        Object.defineProperty(navigator, "deviceMemory", {
                                            get: function() {
                                                return 8;
                                            },
                                        }),
                                        navigator.connection &&
                                        Object.defineProperty(navigator.connection, "rtt", {
                                            get: function() {
                                                return 50 + Math.floor(40 * Math.random());
                                            },
                                        }),
                                        navigator.permissions &&
                                        Object.defineProperty(navigator.permissions, "query", {
                                            value: function() {
                                                return Promise.resolve({
                                                    state: "prompt",
                                                    onchange: null,
                                                });
                                            },
                                        }),
                                        e(),
                                        (window.chrome = {
                                            runtime: {},
                                            loadTimes: function() {
                                                return {
                                                    firstPaintTime: 0,
                                                    firstPaintAfterLoadTime: 0,
                                                    navigationType: "Other",
                                                    requestTime: Date.now() / 1e3,
                                                    startLoadTime: Date.now() / 1e3,
                                                    finishDocumentLoadTime: Date.now() / 1e3,
                                                    finishLoadTime: Date.now() / 1e3,
                                                    firstPaintChromeTime: Date.now() / 1e3,
                                                    wasAlternateProtocolAvailable: !1,
                                                    wasFetchedViaSpdy: !1,
                                                    wasNpnNegotiated: !1,
                                                    npnNegotiatedProtocol: "http/1.1",
                                                    connectionInfo: "h2",
                                                };
                                            },
                                            app: {
                                                isInstalled: !1,
                                                getDetails: function() {},
                                                getIsInstalled: function() {},
                                                installState: function() {
                                                    return "disabled";
                                                },
                                                runningState: function() {
                                                    return "cannot_run";
                                                },
                                            },
                                            csi: function() {
                                                return {
                                                    startE: Date.now(),
                                                    onloadT: Date.now(),
                                                    pageT: Date.now(),
                                                    tran: 15,
                                                };
                                            },
                                        }),
                                        window.Notification &&
                                        Object.defineProperty(window.Notification, "permission", {
                                            get: () => "default",
                                        }),
                                        t(),
                                        n(),
                                        o(),
                                        i());
                                } catch (e) {}
                            });
                        });
                    }
                };
            },
            857: (e) => {
                e.exports = require("os");
            },
            1172: function(e, t, n) {
                var o =
                    (this && this.__decorate) ||
                    function(e, t, n, o) {
                        var i,
                            r = arguments.length,
                            s =
                            r < 3 ?
                            t :
                            null === o ?
                            (o = Object.getOwnPropertyDescriptor(t, n)) :
                            o;
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.decorate
                        )
                            s = Reflect.decorate(e, t, n, o);
                        else
                            for (var a = e.length - 1; a >= 0; a--)
                                (i = e[a]) &&
                                (s = (r < 3 ? i(s) : r > 3 ? i(t, n, s) : i(t, n)) || s);
                        return (r > 3 && s && Object.defineProperty(t, n, s), s);
                    },
                    i =
                    (this && this.__metadata) ||
                    function(e, t) {
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.metadata
                        )
                            return Reflect.metadata(e, t);
                    },
                    r =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.StoreApiImpl = void 0));
                const s = n(7531),
                    a = n(4759),
                    c = n(1969);
                class u extends s.StoreApi {
                        getItem(e) {
                            return r(this, void 0, void 0, function*() {
                                return (0, c.getGlobal)(e);
                            });
                        }
                        setItem(e, t) {
                            return r(this, void 0, void 0, function*() {
                                (0, c.setGlobal)(e, t);
                            });
                        }
                        removeItem(e) {
                            return r(this, void 0, void 0, function*() {
                                (0, c.removeGlobal)(e);
                            });
                        }
                        clear() {
                            return r(this, void 0, void 0, function*() {
                                (0, c.clearGlobal)();
                            });
                        }
                        getPortItem(e) {
                            return r(this, void 0, void 0, function*() {
                                const t = this.getPort();
                                return (0, c.get)(e, t);
                            });
                        }
                        setPortItem(e, t) {
                            return r(this, void 0, void 0, function*() {
                                const n = this.getPort();
                                (0, c.set)(e, t, n);
                            });
                        }
                        removePortItem(e) {
                            return r(this, void 0, void 0, function*() {
                                const t = this.getPort();
                                (0, c.remove)(e, t);
                            });
                        }
                        clearPort() {
                            return r(this, void 0, void 0, function*() {
                                const e = this.getPort();
                                (0, c.clear)(e);
                            });
                        }
                        getPortConfig() {
                            return r(this, void 0, void 0, function*() {
                                const e = this.getPort();
                                return {
                                    success: !0,
                                    data: (0, c.getPortConfig)(e),
                                    port: e
                                };
                            });
                        }
                        copyPortConfig(e, t) {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return (
                                        (0, c.copyPortConfig)(e, t), {
                                            success: !0,
                                            message: `配置从端口 ${e} 复制到端口 ${t} 成功`,
                                        }
                                    );
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "复制配置失败: " + e.message
                                    };
                                }
                            });
                        }
                        getAllPortConfigs() {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return {
                                        success: !0,
                                        data: (0, c.getAllPortConfigs)()
                                    };
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "获取端口配置失败: " + e.message,
                                    };
                                }
                            });
                        }
                        getPortConfigByPort(e) {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return {
                                        success: !0,
                                        data: (0, c.getPortConfig)(e),
                                        port: e
                                    };
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "获取端口配置失败: " + e.message,
                                    };
                                }
                            });
                        }
                        getAllStoreKeys() {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return {
                                        success: !0,
                                        data: (0, c.getAllStoreKeys)()
                                    };
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "获取存储键名失败: " + e.message,
                                    };
                                }
                            });
                        }
                        setPortItemByPort(e, t, n) {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return (
                                        (0, c.set)(t, n, e), {
                                            success: !0,
                                            message: `配置已保存到端口 ${e}`
                                        }
                                    );
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "保存配置失败: " + e.message
                                    };
                                }
                            });
                        }
                        deletePortConfig(e) {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    return (
                                        (0, c.clear)(e), {
                                            success: !0,
                                            message: `端口 ${e} 的配置已删除`
                                        }
                                    );
                                } catch (e) {
                                    return {
                                        success: !1,
                                        message: "删除配置失败: " + e.message
                                    };
                                }
                            });
                        }
                    }
                    ((t.StoreApiImpl = u),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String, Object]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "setItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "removeItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "clear",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getPortItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String, Object]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "setPortItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "removePortItem",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "clearPort",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getPortConfig",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number, Number]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "copyPortConfig",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getAllPortConfigs",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getPortConfigByPort",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "getAllStoreKeys",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number, String, Object]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "setPortItemByPort",
                            null,
                        ),
                        o(
                            [
                                (0, a.InvokeType)(a.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number]),
                                i("design:returntype", Promise),
                            ],
                            u.prototype,
                            "deletePortConfig",
                            null,
                        ));
            },
            1181: (e) => {
                e.exports = require("electron-log");
            },
            1321: (e) => {
                e.exports = require("reflect-metadata");
            },
            1357: (e, t) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DyUser = void 0),
                    (t.DyUser = class {
                        constructor(e, t, n, o, i, r, s = !1, a = !1, c) {
                            ((this.uid = e),
                                (this.secUid = t),
                                (this.isLogin = n),
                                (this.nickName = o),
                                (this.sessionId = i),
                                (this.token = r),
                                (this.isLock = s),
                                (this.isExcepiton = a),
                                (this.lockTime = c));
                        }
                    }));
            },
            1643: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.ProxyService = void 0));
                const r = n(1969),
                    s = i(n(1181));
                class a {
                    constructor() {
                        ((this.PROXY_CONFIG_KEY = "proxy_config"),
                            s.default.info("[ProxyService] 初始化"));
                    }
                    static getInstance() {
                        return (a.instance || (a.instance = new a()), a.instance);
                    }
                    getProxyConfig(e) {
                        return o(this, void 0, void 0, function*() {
                            try {
                                s.default.info(
                                    `[ProxyService] 获取代理配置, 端口: ${e || "默认"}`,
                                );
                                const t = (0, r.get)(this.PROXY_CONFIG_KEY);
                                if (!t)
                                    return void s.default.info("[ProxyService] 未找到代理配置");
                                const n = t.config || t;
                                return n && n.enabled ?
                                    n.server && n.port ?
                                    (s.default.info(
                                            `[ProxyService] 找到有效的代理配置: ${n.server}:${n.port}`,
                                        ),
                                        n) :
                                    void s.default.info(
                                        "[ProxyService] 代理配置缺少服务器或端口信息",
                                    ) :
                                    void s.default.info("[ProxyService] 代理未启用或配置无效");
                            } catch (e) {
                                return void s.default.error(
                                    "[ProxyService] 获取代理配置失败:",
                                    e,
                                );
                            }
                        });
                    }
                    getPlaywrightProxyConfig(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield this.getProxyConfig(e);
                            if (!t) return;
                            const n = {
                                server: `http://${t.server}:${t.port}`
                            };
                            return (
                                t.username &&
                                t.password &&
                                ((n.username = t.username), (n.password = t.password)),
                                s.default.info(
                                    `[ProxyService] 创建Playwright代理配置: ${JSON.stringify(n)}`,
                                ),
                                n
                            );
                        });
                    }
                }
                t.ProxyService = a;
            },
            1682: (e) => {
                e.exports = require("conf");
            },
            1954: function(e, t, n) {
                var o,
                    i =
                    (this && this.__createBinding) ||
                    (Object.create ?
                        function(e, t, n, o) {
                            void 0 === o && (o = n);
                            var i = Object.getOwnPropertyDescriptor(t, n);
                            ((i &&
                                    !("get" in i ?
                                        !t.__esModule :
                                        i.writable || i.configurable)) ||
                                (i = {
                                    enumerable: !0,
                                    get: function() {
                                        return t[n];
                                    },
                                }),
                                Object.defineProperty(e, o, i));
                        } :
                        function(e, t, n, o) {
                            (void 0 === o && (o = n), (e[o] = t[n]));
                        }),
                    r =
                    (this && this.__setModuleDefault) ||
                    (Object.create ?
                        function(e, t) {
                            Object.defineProperty(e, "default", {
                                enumerable: !0,
                                value: t,
                            });
                        } :
                        function(e, t) {
                            e.default = t;
                        }),
                    s =
                    (this && this.__importStar) ||
                    ((o = function(e) {
                            return (
                                (o =
                                    Object.getOwnPropertyNames ||
                                    function(e) {
                                        var t = [];
                                        for (var n in e)
                                            Object.prototype.hasOwnProperty.call(e, n) &&
                                            (t[t.length] = n);
                                        return t;
                                    }),
                                o(e)
                            );
                        }),
                        function(e) {
                            if (e && e.__esModule) return e;
                            var t = {};
                            if (null != e)
                                for (var n = o(e), s = 0; s < n.length; s++)
                                    "default" !== n[s] && i(t, e, n[s]);
                            return (r(t, e), t);
                        });
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.HttpCryptoService = void 0));
                const a = s(n(8749));
                class c {
                    static encrypt(e) {
                        try {
                            const t = "string" == typeof e ? e : JSON.stringify(e),
                                n = this.generateSalt(this.SALT_LENGTH),
                                o = this.deriveKey(this.SECRET_KEY, n),
                                i = a.randomBytes(16),
                                r = a.createCipheriv("aes-256-cbc", o, i);
                            let s = r.update(t, "utf8", "base64");
                            s += r.final("base64");
                            const c = i.toString("base64");
                            return `${this.MARKER}${n}${c}${s}`;
                        } catch (e) {
                            throw (console.error("加密失败:", e), new Error("加密失败"));
                        }
                    }
                    static decrypt(e) {
                        try {
                            if (!e || !e.startsWith(this.MARKER))
                                throw new Error("无效的加密数据");
                            const t = e.substring(1, this.SALT_LENGTH + 1),
                                n = e.substring(this.SALT_LENGTH + 1, this.SALT_LENGTH + 25),
                                o = e.substring(this.SALT_LENGTH + 25),
                                i = this.deriveKey(this.SECRET_KEY, t),
                                r = Buffer.from(n, "base64"),
                                s = a.createDecipheriv("aes-256-cbc", i, r);
                            let c = s.update(o, "base64", "utf8");
                            c += s.final("utf8");
                            try {
                                return JSON.parse(c);
                            } catch (e) {
                                return c;
                            }
                        } catch (e) {
                            throw (console.error("解密失败:", e), new Error("解密失败"));
                        }
                    }
                    static deriveKey(e, t) {
                        return a.pbkdf2Sync(e, t, 1e3, 32, "sha256");
                    }
                    static generateSalt(e) {
                        let t = "";
                        for (let n = 0; n < e; n++)
                            t +=
                            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(
                                Math.floor(62 * Math.random()),
                            );
                        return t;
                    }
                    static isEncrypted(e) {
                        return "string" == typeof e && e.startsWith(this.MARKER);
                    }
                }
                ((t.HttpCryptoService = c),
                    (c.MARKER = "@"),
                    (c.SALT_LENGTH = 8),
                    (c.SECRET_KEY = "dY@pP$3cuR1tY"));
            },
            1969: (e, t) => {
                let n;
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.initStore = function(e) {
                        n = e;
                    }),
                    (t.setCurrentPort = function(e) {
                        o = e;
                    }),
                    (t.getCurrentPort = function() {
                        return o;
                    }),
                    (t.getGlobal = function(e) {
                        return n.get(e);
                    }),
                    (t.setGlobal = function(e, t) {
                        n.set(e, t);
                    }),
                    (t.removeGlobal = function(e) {
                        n.delete(e);
                    }),
                    (t.clearGlobal = function() {
                        n.clear();
                    }),
                    (t.get = function(e, t) {
                        const o = i(e, t);
                        return n.get(o);
                    }),
                    (t.set = r),
                    (t.remove = function(e, t) {
                        const o = i(e, t);
                        n.delete(o);
                    }),
                    (t.clear = function(e) {
                        const t = e || o;
                        if (0 === t)
                            return void console.warn(
                                "尝试清空端口0的配置，操作被忽略。请使用clearGlobal()清空全局配置。",
                            );
                        const i = Object.keys(n.store),
                            r = `port_${t}_`;
                        i.forEach((e) => {
                            e.startsWith(r) && n.delete(e);
                        });
                    }),
                    (t.getPortConfig = s),
                    (t.copyPortConfig = function(e, t) {
                        const n = s(e);
                        Object.keys(n).forEach((e) => {
                            r(e, n[e], t);
                        });
                    }),
                    (t.getPortConfigStats = function() {
                        const e = Object.keys(n.store),
                            t = {};
                        return (
                            e.forEach((e) => {
                                const n = e.match(/^port_(\d+)_/);
                                if (n) {
                                    const e = parseInt(n[1]);
                                    t[e] = (t[e] || 0) + 1;
                                }
                            }),
                            t
                        );
                    }),
                    (t.getAllPortConfigs = function() {
                        const e = Object.keys(n.store),
                            t = {};
                        return (
                            e.forEach((e) => {
                                const o = e.match(/^port_(\d+)_(.+)/);
                                if (o) {
                                    const i = parseInt(o[1]),
                                        r = o[2];
                                    (t[i] || (t[i] = {}), (t[i][r] = n.get(e)));
                                }
                            }),
                            t
                        );
                    }),
                    (t.getAllStoreKeys = function() {
                        return Object.keys(n.store);
                    }));
                let o = 0;

                function i(e, t) {
                    const n = t || o;
                    return 0 === n ? e : `port_${n}_${e}`;
                }

                function r(e, t, o) {
                    const r = i(e, o);
                    n.set(r, t);
                }

                function s(e) {
                    const t = Object.keys(n.store),
                        o = `port_${e}_`,
                        i = {};
                    return (
                        t.forEach((e) => {
                            if (e.startsWith(o)) {
                                const t = e.replace(o, "");
                                i[t] = n.get(e);
                            }
                        }),
                        i
                    );
                }
            },
            2407: function(e, t, n) {
                var o,
                    i =
                    (this && this.__createBinding) ||
                    (Object.create ?
                        function(e, t, n, o) {
                            void 0 === o && (o = n);
                            var i = Object.getOwnPropertyDescriptor(t, n);
                            ((i &&
                                    !("get" in i ?
                                        !t.__esModule :
                                        i.writable || i.configurable)) ||
                                (i = {
                                    enumerable: !0,
                                    get: function() {
                                        return t[n];
                                    },
                                }),
                                Object.defineProperty(e, o, i));
                        } :
                        function(e, t, n, o) {
                            (void 0 === o && (o = n), (e[o] = t[n]));
                        }),
                    r =
                    (this && this.__setModuleDefault) ||
                    (Object.create ?
                        function(e, t) {
                            Object.defineProperty(e, "default", {
                                enumerable: !0,
                                value: t,
                            });
                        } :
                        function(e, t) {
                            e.default = t;
                        }),
                    s =
                    (this && this.__importStar) ||
                    ((o = function(e) {
                            return (
                                (o =
                                    Object.getOwnPropertyNames ||
                                    function(e) {
                                        var t = [];
                                        for (var n in e)
                                            Object.prototype.hasOwnProperty.call(e, n) &&
                                            (t[t.length] = n);
                                        return t;
                                    }),
                                o(e)
                            );
                        }),
                        function(e) {
                            if (e && e.__esModule) return e;
                            var t = {};
                            if (null != e)
                                for (var n = o(e), s = 0; s < n.length; s++)
                                    "default" !== n[s] && i(t, e, n[s]);
                            return (r(t, e), t);
                        }),
                    a =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    c =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.enableUpdateInDev = function() {
                        (Object.defineProperty(d.app, "isPackaged", {
                                get: () => !1
                            }),
                            (u.autoUpdater.logger = l.default),
                            (u.autoUpdater.logger.transports.file.level = "debug"),
                            l.default.info("已在开发环境中启用更新检查功能"));
                        const e =
                            process.env.QINIU_YUN_URL ||
                            "https://dybenben.oss-cn-hongkong.aliyuncs.com/x-zhihui-assistant";
                        return (
                            l.default.info("设置更新 URL:", e),
                            u.autoUpdater.setFeedURL({
                                provider: "generic",
                                url: `${e}/updates/`,
                            }),
                            u.autoUpdater
                        );
                    }),
                    (t.checkForUpdates = function() {
                        return a(this, void 0, void 0, function*() {
                            if (g || y)
                                l.default.info(
                                    "更新检查被跳过：updateFlag=" +
                                    g +
                                    ", isUpdateAvailable=" +
                                    y,
                                );
                            else {
                                g = !0;
                                try {
                                    l.default.info("开始检查更新...");
                                    const e = yield u.autoUpdater.checkForUpdates();
                                    l.default.info("更新检查结果:", e);
                                } catch (e) {
                                    l.default.error("更新检查失败:", e);
                                } finally {
                                    ((g = !1), l.default.info("更新检查完成，解除锁定"));
                                }
                            }
                        });
                    }),
                    (t.setupAutoUpdater = function(e) {
                        if (
                            (l.default.info("开始设置自动更新..."),
                                (function() {
                                    const e = h.join(d.app.getPath("userData"), "updater");
                                    (l.default.info("更新目录路径:", e),
                                        f.existsSync(e) ||
                                        (l.default.info("创建更新目录..."),
                                            f.mkdirSync(e, {
                                                recursive: !0
                                            })));
                                })(),
                                (u.autoUpdater.autoInstallOnAppQuit = !0),
                                (u.autoUpdater.autoDownload = !1),
                                (u.autoUpdater.allowPrerelease = !0),
                                l.default.info("更新配置已设置"),
                                (u.autoUpdater.requestHeaders = {
                                    "User-Agent": "Electron"
                                }),
                                "win32" === process.platform)
                        ) {
                            const e = {
                                provider: "generic",
                                url: `${process.env.QINIU_YUN_URL || "https://dybenben.oss-cn-hongkong.aliyuncs.com/x-zhihui-assistant"}/updates/`,
                            };
                            (l.default.info("Windows 平台更新 URL:", e),
                                u.autoUpdater.setFeedURL(e));
                        }
                        (u.autoUpdater.on("checking-for-update", () => {
                                l.default.info("正在检查更新...");
                            }),
                            u.autoUpdater.on("update-available", (e) => {
                                (l.default.info("发现新版本:", e.version), (y = !0));
                                const t = e.version,
                                    n = e.releaseNotes || "",
                                    o = e.releaseName || "新版本",
                                    i = e,
                                    r = !0 === i.forceUpdate,
                                    s = i.updateType || "normal",
                                    a = new d.BrowserWindow({
                                        width: 800,
                                        height: 600,
                                        alwaysOnTop: !0,
                                        autoHideMenuBar: !0,
                                        webPreferences: {
                                            preload: h.join(__dirname, "preload.js"),
                                            contextIsolation: !0,
                                            webviewTag: !0,
                                            webSecurity: !1,
                                            nodeIntegration: !0,
                                        },
                                    }),
                                    c =
                                    "string" == typeof n ?
                                    n :
                                    Array.isArray(n) ?
                                    n
                                    .map((e) => ("string" == typeof e ? e : e.note))
                                    .join("\n") :
                                    "";
                                (0, p.setUpdateWindow)(a);
                                const u = `${process.env.WEBVIEW_URL}/installer?version=${t}&releaseNotes=${encodeURIComponent(c)}&releaseName=${encodeURIComponent(o)}&forceUpdate=${r}&updateType=${s}`;
                                (a.loadURL(u), a.on("closed", () => {}));
                            }),
                            u.autoUpdater.on("update-not-available", () =>
                                a(this, void 0, void 0, function*() {
                                    l.default.info("当前已是最新版本");
                                }),
                            ),
                            u.autoUpdater.on("error", (e) => {
                                (l.default.error("更新出错:", e),
                                    v &&
                                    (l.default.info(
                                            "这是开发环境。更新错误可能是因为DMG/EXE文件格式不兼容。",
                                        ),
                                        l.default.info(
                                            '您可以通过"调试"菜单中的"模拟更新可用"来测试更新流程UI。',
                                        )),
                                    (g = !1));
                            }),
                            l.default.info("自动更新设置完成"));
                    }));
                const u = n(6238),
                    l = c(n(1181)),
                    d = n(4482),
                    f = s(n(9896)),
                    h = s(n(6928)),
                    p = n(5857);
                let g = !1,
                    y = !1,
                    v = !d.app.isPackaged;
                d.ipcMain.on("user-update-confirm", (e, t) => {
                    t && u.autoUpdater.downloadUpdate();
                });
            },
            2686: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DyLoginSmsMonitor =
                        t.DyLoginSmsValidateMonitor =
                        t.SendSmsCodeMonitor =
                        void 0));
                const r = n(3466),
                    s = n(8922),
                    a = i(n(1181));
                class c extends s.MonitorResponse {
                    isMatch(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return !!e.includes("/passport/web/send_code/");
                        });
                    }
                    getType() {
                        return "sendSmsCode";
                    }
                    getKey() {
                        return "sendSmsCode";
                    }
                    getResponseData(e) {
                        return o(this, void 0, void 0, function*() {
                            var t;
                            const n = yield e.json();
                            if (
                                (a.default.info("login sms monitor result is ", n),
                                    "error" == n.message)
                            ) {
                                const e =
                                    null === (t = n.data) || void 0 === t ?
                                    void 0 :
                                    t.description;
                                return new r.DoorEntity(!1, e);
                            }
                            return "success" == n.message ?
                                new r.DoorEntity(!0, n) :
                                new r.DoorEntity(!1, "发送验证码失败");
                        });
                    }
                }
                t.SendSmsCodeMonitor = c;
                class u extends s.MonitorResponse {
                    isMatch(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return !!e.includes("/passport/web/validate_code/");
                        });
                    }
                    getType() {
                        return "smsLoginValidate";
                    }
                    getKey() {
                        return "smsLoginValidate";
                    }
                    getResponseData(e) {
                        return o(this, void 0, void 0, function*() {
                            var t;
                            const n = yield e.json();
                            if (
                                (a.default.info("login sms monitor result is ", n),
                                    "error" == n.message)
                            ) {
                                const e =
                                    null === (t = n.data) || void 0 === t ?
                                    void 0 :
                                    t.description;
                                return new r.DoorEntity(!1, e);
                            }
                            return "success" == n.message ?
                                new r.DoorEntity(!0, n) :
                                new r.DoorEntity(!1, "验证码错误");
                        });
                    }
                }
                t.DyLoginSmsValidateMonitor = u;
                class l extends s.MonitorResponse {
                    isMatch(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return !!e.includes("/passport/web/sms_login/");
                        });
                    }
                    getType() {
                        return "smsLogin";
                    }
                    getKey() {
                        return "smsLogin";
                    }
                    getResponseData(e) {
                        return o(this, void 0, void 0, function*() {
                            var t;
                            const n = e.headers()["content-type"];
                            if (n.includes("application/json"))
                                try {
                                    const n = yield e.json();
                                    if (
                                        (a.default.info("login sms monitor result is ", n),
                                            "error" == n.message)
                                    ) {
                                        const e =
                                            null === (t = n.data) || void 0 === t ?
                                            void 0 :
                                            t.description;
                                        return new r.DoorEntity(!1, e);
                                    }
                                    return "success" == n.message ?
                                        new r.DoorEntity(!0, n) :
                                        new r.DoorEntity(!1, n);
                                } catch (e) {
                                    return new r.DoorEntity(!0, "登录成功");
                                }
                            return n && n.includes("text/html") ?
                                new r.DoorEntity(!0, "登录成功") :
                                new r.DoorEntity(!1, "登录失败,请尝试扫码登录");
                        });
                    }
                }
                t.DyLoginSmsMonitor = l;
            },
            2749: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.OneLoginMonitor = t.DyLoginMonitor = void 0));
                const r = n(3466),
                    s = n(8922),
                    a = i(n(1181));
                class c extends s.MonitorResponse {
                    isMatch(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return !!e.includes("web/aweme/favorite/");
                        });
                    }
                    getType() {
                        return "login";
                    }
                    getKey() {
                        return "loginData";
                    }
                    getResponseData(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield e.json();
                            return new r.DoorEntity(!0, t);
                        });
                    }
                    needHeaderData() {
                        return !0;
                    }
                }
                t.DyLoginMonitor = c;
                class u extends s.MonitorResponse {
                    isMatch(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return !!e.includes("passport/web/one_login/");
                        });
                    }
                    getType() {
                        return "onelogin";
                    }
                    getKey() {
                        return "onelogin";
                    }
                    needHeaderData() {
                        return !0;
                    }
                    getResponseData(e) {
                        return o(this, void 0, void 0, function*() {
                            var t;
                            const n = e.headers()["content-type"];
                            if (n.includes("application/json"))
                                try {
                                    const n = yield e.json();
                                    if (
                                        (a.default.info("oneLogin result is ", n),
                                            "error" == n.message)
                                    ) {
                                        const e =
                                            null === (t = n.data) || void 0 === t ?
                                            void 0 :
                                            t.description;
                                        return new r.DoorEntity(!1, e);
                                    }
                                    return "success" == n.message ?
                                        new r.DoorEntity(!0, n) :
                                        new r.DoorEntity(!1, n);
                                } catch (e) {
                                    return new r.DoorEntity(!0, "登录成功");
                                }
                            return n && n.includes("text/html") ?
                                new r.DoorEntity(!0, "登录成功") :
                                new r.DoorEntity(!1, "登录失败,请尝试扫码登录");
                        });
                    }
                }
                t.OneLoginMonitor = u;
            },
            3116: (e, t, n) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DyEngine = void 0));
                const o = n(835);
                class i extends o.DoorEngine {
                    doWaitFor(e, t) {
                        throw new Error("Method not implemented.");
                    }
                    doCallback(e) {
                        throw new Error("Method not implemented.");
                    }
                    getNamespace() {
                        return "dy";
                    }
                }
                t.DyEngine = i;
            },
            3406: function(e, t, n) {
                var o =
                    (this && this.__decorate) ||
                    function(e, t, n, o) {
                        var i,
                            r = arguments.length,
                            s =
                            r < 3 ?
                            t :
                            null === o ?
                            (o = Object.getOwnPropertyDescriptor(t, n)) :
                            o;
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.decorate
                        )
                            s = Reflect.decorate(e, t, n, o);
                        else
                            for (var a = e.length - 1; a >= 0; a--)
                                (i = e[a]) &&
                                (s = (r < 3 ? i(s) : r > 3 ? i(t, n, s) : i(t, n)) || s);
                        return (r > 3 && s && Object.defineProperty(t, n, s), s);
                    },
                    i =
                    (this && this.__metadata) ||
                    function(e, t) {
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.metadata
                        )
                            return Reflect.metadata(e, t);
                    },
                    r =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    s =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.InstallerImpl = void 0));
                const a = n(9154),
                    c = n(6238),
                    u = s(n(1181)),
                    l = n(4482),
                    d = n(5857),
                    f = n(4759);
                class h extends a.InstallerApi {
                        constructor() {
                            (super(), (this.isDownloading = !1), this.setupAutoUpdater());
                        }
                        sendMessage(e, ...t) {
                            null === d.updateWindow ||
                                void 0 === d.updateWindow ||
                                d.updateWindow.webContents.send(e, ...t);
                        }
                        setupAutoUpdater() {
                            ((c.autoUpdater.autoDownload = !1),
                                (c.autoUpdater.autoInstallOnAppQuit = !1),
                                c.autoUpdater.on("download-progress", (e) => {
                                    const t = Math.round(e.percent);
                                    (u.default.info(`下载进度: ${t}%`),
                                        this.send("onMonitorDownloadProgress", t));
                                }),
                                c.autoUpdater.on("update-downloaded", (e) => {
                                    (u.default.info("下载完成，准备安装..."),
                                        this.send("onMonitorUpdateDownloaded", {
                                            version: e.version,
                                            releaseNotes: e.releaseNotes,
                                        }));
                                }),
                                c.autoUpdater.on("error", (e) => {
                                    (u.default.error("更新出错:", e),
                                        this.send("onMonitorUpdateDownloadedError", e));
                                }));
                        }
                        update() {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    ((this.isDownloading = !0),
                                        yield c.autoUpdater.downloadUpdate());
                                } catch (e) {
                                    throw (
                                        (this.isDownloading = !1),
                                        this.send("onMonitorUpdateDownloadedError", e),
                                        e
                                    );
                                }
                            });
                        }
                        cancelUpdate() {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    ((this.isDownloading = !1),
                                        null === d.updateWindow ||
                                        void 0 === d.updateWindow ||
                                        d.updateWindow.close(),
                                        l.app.quit());
                                } catch (e) {
                                    throw (this.send("onMonitorUpdateDownloadedError", e), e);
                                }
                            });
                        }
                        install() {
                            return r(this, void 0, void 0, function*() {
                                try {
                                    c.autoUpdater.quitAndInstall();
                                } catch (e) {
                                    throw (this.send("onMonitorUpdateDownloadedError", e), e);
                                }
                            });
                        }
                    }
                    ((t.InstallerImpl = h),
                        o(
                            [
                                (0, f.InvokeType)(f.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            h.prototype,
                            "update",
                            null,
                        ),
                        o(
                            [
                                (0, f.InvokeType)(f.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            h.prototype,
                            "cancelUpdate",
                            null,
                        ),
                        o(
                            [
                                (0, f.InvokeType)(f.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            h.prototype,
                            "install",
                            null,
                        ));
            },
            3423: (e, t, n) => {
                const o = n(6928),
                    {
                        app: i,
                        ipcMain: r,
                        ipcRenderer: s,
                        shell: a
                    } = n(4482),
                    c = n(1682);
                let u = !1;
                const l = () => {
                    if (!r || !i)
                        throw new Error(
                            "Electron Store: You need to call `.initRenderer()` from the main process.",
                        );
                    const e = {
                        defaultCwd: i.getPath("userData"),
                        appVersion: i.getVersion(),
                    };
                    return (
                        u ||
                        (r.on("electron-store-get-data", (t) => {
                                t.returnValue = e;
                            }),
                            (u = !0)),
                        e
                    );
                };
                e.exports = class extends c {
                    constructor(e) {
                        let t, n;
                        if (s) {
                            const e = s.sendSync("electron-store-get-data");
                            if (!e)
                                throw new Error(
                                    "Electron Store: You need to call `.initRenderer()` from the main process.",
                                );
                            ({
                                defaultCwd: t,
                                appVersion: n
                            } = e);
                        } else r && i && ({
                            defaultCwd: t,
                            appVersion: n
                        } = l());
                        ((e = {
                                name: "config",
                                ...e
                            }).projectVersion ||
                            (e.projectVersion = n),
                            e.cwd ?
                            (e.cwd = o.isAbsolute(e.cwd) ? e.cwd : o.join(t, e.cwd)) :
                            (e.cwd = t),
                            (e.configName = e.name),
                            delete e.name,
                            super(e));
                    }
                    static initRenderer() {
                        l();
                    }
                    openInEditor() {
                        a.openPath(this.path);
                    }
                };
            },
            3466: (e, t) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DoorEntity = void 0),
                    (t.DoorEntity = class {
                        constructor(
                            e = !0,
                            t = {},
                            n = "",
                            o = {},
                            i = {},
                            r = void 0,
                            s = {},
                        ) {
                            ((this.code = e),
                                (this.data = t),
                                (this.headerData = o),
                                (this.requestBody = i),
                                (this.url = n),
                                (this.validateUrl = r),
                                (this.responseHeaderData = s));
                        }
                        setValidateParams(e) {
                            this.validateParams = e;
                        }
                        getValidateParams() {
                            return this.validateParams;
                        }
                        getValidateUrl() {
                            return this.validateUrl;
                        }
                        getRequestBody() {
                            return this.requestBody;
                        }
                        getCode() {
                            return this.code;
                        }
                        getData() {
                            return this.data;
                        }
                        getHeaderData() {
                            return this.headerData;
                        }
                        getUrl() {
                            return this.url;
                        }
                        getResponseHeaderData() {
                            return this.responseHeaderData;
                        }
                    }));
            },
            3669: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.SecureHttpClient = void 0));
                const r = i(n(8938)),
                    s = n(1954);
                t.SecureHttpClient = class {
                    constructor(e, t = !0, n = {}) {
                        ((this.enableEncryption = t),
                            (this.axiosInstance = r.default.create(
                                Object.assign(Object.assign({
                                    baseURL: e,
                                    timeout: 12e4
                                }, n), {
                                    headers: Object.assign({
                                            "Content-Type": "application/json"
                                        },
                                        n.headers || {},
                                    ),
                                }),
                            )),
                            this.setupInterceptors());
                    }
                    setupInterceptors() {
                        (this.axiosInstance.interceptors.request.use(
                                (e) => {
                                    var t;
                                    return (
                                        this.enableEncryption &&
                                        e.data && ["post", "put", "patch"].includes(
                                            (null === (t = e.method) || void 0 === t ?
                                                void 0 :
                                                t.toLowerCase()) || "",
                                        ) &&
                                        ((e.data = {
                                                encryptedData: s.HttpCryptoService.encrypt(e.data),
                                            }),
                                            e.headers && (e.headers["X-Encrypted"] = "true")),
                                        e
                                    );
                                },
                                (e) => Promise.reject(e),
                            ),
                            this.axiosInstance.interceptors.response.use(
                                (e) => {
                                    if (this.enableEncryption && e.data && e.data.encryptedData)
                                        try {
                                            const t = s.HttpCryptoService.decrypt(
                                                e.data.encryptedData,
                                            );
                                            e.data = t;
                                        } catch (e) {}
                                    return e;
                                },
                                (e) => Promise.reject(e),
                            ));
                    }
                    get(e, t) {
                        return o(this, void 0, void 0, function*() {
                            return this.axiosInstance.get(e, t);
                        });
                    }
                    post(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return this.axiosInstance.post(e, t, n);
                        });
                    }
                    put(e, t, n) {
                        return o(this, void 0, void 0, function*() {
                            return this.axiosInstance.put(e, t, n);
                        });
                    }
                    delete(e, t) {
                        return o(this, void 0, void 0, function*() {
                            return this.axiosInstance.delete(e, t);
                        });
                    }
                    getAxiosInstance() {
                        return this.axiosInstance;
                    }
                    setEncryption(e) {
                        this.enableEncryption = e;
                    }
                };
            },
            4005: (e, t, n) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.decryptRequest = i),
                    (t.encryptResponse = r),
                    (t.secureMiddleware = function(e, t, n) {
                        i(e, t, (o) => {
                            if (o) return n(o);
                            r(e, t, n);
                        });
                    }));
                const o = n(1954);

                function i(e, t, n) {
                    try {
                        if (
                            "true" === e.headers["x-encrypted"] &&
                            e.body &&
                            e.body.encryptedData
                        ) {
                            const n = e.body.encryptedData;
                            try {
                                const t = o.HttpCryptoService.decrypt(n);
                                e.body = t;
                            } catch (e) {
                                return t
                                    .status(400)
                                    .json({
                                        success: !1,
                                        message: "请求解密失败",
                                        code: 400
                                    });
                            }
                        }
                        n();
                    } catch (e) {
                        t.status(500).json({
                            success: !1,
                            message: "请求处理失败",
                            code: 500,
                        });
                    }
                }

                function r(e, t, n) {
                    const i = t.json;
                    ((t.json = function(t) {
                            if ("true" === e.headers["x-encrypted"])
                                try {
                                    t = {
                                        encryptedData: o.HttpCryptoService.encrypt(t)
                                    };
                                } catch (e) {
                                    t = Object.assign(Object.assign({}, t), {
                                        _encryption_failed: !0,
                                    });
                                }
                            return i.call(this, t);
                        }),
                        n());
                }
            },
            4242: function(e, t, n) {
                var o =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.PortManager = void 0));
                const i = o(n(3423)),
                    r = o(n(1181));
                class s {
                    constructor() {
                        ((this.portStore = new i.default({
                                name: "port-manager"
                            })),
                            (this.activeInstances = new Map()),
                            (this.availablePorts = []),
                            this.initializePortRange());
                    }
                    static getInstance() {
                        return (s.instance || (s.instance = new s()), s.instance);
                    }
                    initializePortRange() {
                        for (let e = 1; e <= 65535; e++) this.availablePorts.push(e);
                        const e = this.portStore.get("allocatedPorts", []);
                        ((this.availablePorts = this.availablePorts.filter(
                                (t) => !e.includes(t),
                            )),
                            r.default.info(
                                `PortManager initialized with ${this.availablePorts.length} available ports`,
                            ));
                    }
                    allocatePort() {
                        if (0 === this.availablePorts.length)
                            throw new Error("No available ports");
                        const e = this.availablePorts.shift(),
                            t = this.portStore.get("allocatedPorts", []);
                        return (
                            t.push(e),
                            this.portStore.set("allocatedPorts", t),
                            r.default.info(`Port ${e} allocated`),
                            e
                        );
                    }
                    releasePort(e) {
                        (this.activeInstances.has(e) && this.activeInstances.delete(e),
                            this.availablePorts.includes(e) || this.availablePorts.push(e));
                        const t = this.portStore
                            .get("allocatedPorts", [])
                            .filter((t) => t !== e);
                        (this.portStore.set("allocatedPorts", t),
                            r.default.info(`Port ${e} released`));
                    }
                    registerInstance(e, t, n) {
                        const o = {
                            port: e,
                            windowId: t,
                            window: n,
                            isActive: !0,
                            createdAt: new Date(),
                            lastActiveAt: new Date(),
                        };
                        (this.activeInstances.set(e, o),
                            n.on("closed", () => {
                                (this.releasePort(e),
                                    r.default.info(`Instance for port ${e} closed`));
                            }),
                            r.default.info(
                                `Instance registered for port ${e} with windowId ${t}`,
                            ));
                    }
                    getInstance(e) {
                        return this.activeInstances.get(e);
                    }
                    getActiveInstances() {
                        return Array.from(this.activeInstances.values());
                    }
                    isPortActive(e) {
                        return this.activeInstances.has(e);
                    }
                    getWindow(e) {
                        const t = this.activeInstances.get(e);
                        return null == t ? void 0 : t.window;
                    }
                    updateLastActive(e) {
                        const t = this.activeInstances.get(e);
                        t && (t.lastActiveAt = new Date());
                    }
                    getAvailablePortCount() {
                        return this.availablePorts.length;
                    }
                    getActiveInstanceCount() {
                        return this.activeInstances.size;
                    }
                    static getPortConfigKey(e, t) {
                        return `port_${e}_${t}`;
                    }
                    cleanup() {
                        (this.activeInstances.forEach((e, t) => {
                                e.window && !e.window.isDestroyed() && e.window.close();
                            }),
                            this.activeInstances.clear(),
                            this.portStore.set("allocatedPorts", []),
                            r.default.info("All ports cleaned up"));
                    }
                }
                t.PortManager = s;
            },
            4393: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.SecureExpressServer = void 0),
                    (t.getSecureServerInstance = d),
                    (t.startSecureServer = function() {
                        return o(this, void 0, void 0, function*() {
                            const e = d();
                            return (yield e.start(), e);
                        });
                    }),
                    (t.stopSecureServer = function() {
                        l && (l.stop(), (l = null));
                    }));
                const r = n(4005),
                    s = n(4543),
                    a = i(n(1181)),
                    c = i(n(7252));
                class u extends s.ExpressServer {
                    constructor() {
                        (super(),
                            this.applySecureMiddleware(),
                            a.default.info("[SecureServer] 已创建安全服务器实例"));
                    }
                    applySecureMiddleware() {
                        (this.getApp().use(r.secureMiddleware),
                            a.default.info("[SecureServer] 已应用安全中间件"));
                    }
                    createSecureRouter(e) {
                        const t = c.default.Router();
                        return (
                            this.addRoutes(e, t),
                            a.default.info(`[SecureServer] 已创建安全路由: ${e}`),
                            t
                        );
                    }
                    start() {
                        const e = Object.create(null, {
                            start: {
                                get: () => super.start
                            },
                        });
                        return o(this, void 0, void 0, function*() {
                            return (
                                a.default.info("[SecureServer] 启动安全服务器..."),
                                e.start.call(this)
                            );
                        });
                    }
                    stop() {
                        (a.default.info("[SecureServer] 停止安全服务器..."), super.stop());
                    }
                }
                t.SecureExpressServer = u;
                let l = null;

                function d() {
                    return (l || (l = new u()), l);
                }
            },
            4482: (e) => {
                e.exports = require("electron");
            },
            4543: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.ExpressServer = void 0));
                const r = i(n(7252)),
                    s = i(n(8577)),
                    a = n(8969),
                    c = n(6836),
                    u = i(n(1181)),
                    l = n(4005);
                t.ExpressServer = class {
                    constructor(e = !0) {
                        ((this.app = (0, r.default)()),
                            (this.port = 23333),
                            (this.useSecureMode = !0),
                            (this.errorHandler = (e, t, n, o) => {
                                if ((console.error("Server error:", e), n.headersSent))
                                    return o(e);
                                const i = e.statusCode || 500,
                                    r = e.message || "Internal Server Error";
                                n.status(i).json(this.createErrorResponse(r, i));
                            }),
                            (this.useSecureMode = e),
                            this.setupMiddleware(),
                            this.setupRoutes(),
                            u.default.info(
                                "ExpressServer 已创建，安全模式: " +
                                (this.useSecureMode ? "启用" : "禁用"),
                            ));
                    }
                    setupMiddleware() {
                        (this.app.use((0, s.default)()),
                            this.app.use(r.default.json()),
                            this.useSecureMode &&
                            (u.default.info("应用安全中间件，启用加密通信"),
                                this.app.use(l.secureMiddleware)),
                            this.app.use(r.default.static("public")));
                    }
                    setupRoutes() {
                        (this.setupLoginRoutes(),
                            this.setupRunRoutes(),
                            this.app.get("/health", (e, t) => {
                                t.json(this.createSuccessResponse({
                                    status: "ok"
                                }));
                            }),
                            this.app.use(this.errorHandler));
                    }
                    setupLoginRoutes() {
                        const e = r.default.Router();
                        (e.post("/await-result", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n,
                                            headless: o = !0
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const i = yield(0, a.awaitByLoginResult)(n, o);
                                        t.json(this.createSuccessResponse(i));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/open-user-info", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const o = yield(0, a.openUserInfo)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/await-qr-result", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const o = yield(0, a.awaitByLoginResultByQR)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/check-validate", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const o = yield(0, a.checkAgainValidate)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/sms-login", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n,
                                            code: o
                                        } = e.body;
                                        if (!n || !o)
                                            return t
                                                .status(400)
                                                .json(
                                                    this.createErrorResponse(
                                                        "端口号和验证码是必需的",
                                                        400,
                                                    ),
                                                );
                                        const i = yield(0, a.loginBySmsCode)(n, o);
                                        t.json(this.createSuccessResponse(i));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/resend-sms", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const o = yield(0, a.againSendSms)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/sms-login-init", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const o = yield(0, a.smsLoginInit)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/get-validate-code", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n,
                                            phone: o
                                        } = e.body;
                                        if (!n || !o)
                                            return t
                                                .status(400)
                                                .json(
                                                    this.createErrorResponse(
                                                        "端口号和手机号是必需的",
                                                        400,
                                                    ),
                                                );
                                        const i = yield(0, a.getValidateCodeByPhone)(n, o);
                                        t.json(this.createSuccessResponse(i));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/login-by-phone", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n,
                                            code: o
                                        } = e.body;
                                        if (!n || !o)
                                            return t
                                                .status(400)
                                                .json(
                                                    this.createErrorResponse(
                                                        "端口号和验证码是必需的",
                                                        400,
                                                    ),
                                                );
                                        const i = yield(0, a.loginByPhone)(n, o);
                                        t.json(this.createSuccessResponse(i));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ));
                        try {
                            this.app.use("/api/login", e);
                        } catch (e) {
                            u.default.error("setupLoginRoutes error:", e);
                        }
                    }
                    setupRunRoutes() {
                        const e = r.default.Router();
                        (e.post("/start", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            port: n,
                                            headless: o
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(this.createErrorResponse("端口号是必需的", 400));
                                        const i = yield(0, c.runByPort)(n, o);
                                        t.json(this.createSuccessResponse(i));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            e.post("/digg", (e, t, n) =>
                                o(this, void 0, void 0, function*() {
                                    try {
                                        const {
                                            taskResponse: n
                                        } = e.body;
                                        if (!n)
                                            return t
                                                .status(400)
                                                .json(
                                                    this.createErrorResponse("taskResponse是必需的", 400),
                                                );
                                        const o = yield(0, c.digg)(n);
                                        t.json(this.createSuccessResponse(o));
                                    } catch (e) {
                                        n(e);
                                    }
                                }),
                            ),
                            this.app.use("/api/run", e));
                    }
                    createSuccessResponse(e, t) {
                        return {
                            success: !0,
                            data: e,
                            message: t || "Success"
                        };
                    }
                    createErrorResponse(e, t = 500) {
                        return {
                            success: !1,
                            message: e,
                            code: t
                        };
                    }
                    getApp() {
                        return this.app;
                    }
                    addRoutes(e, t) {
                        this.app.use(e, t);
                    }
                    setSecureMode(e) {
                        ((this.useSecureMode = e),
                            u.default.info("服务器安全模式已" + (e ? "启用" : "禁用")),
                            e && this.app.use(l.secureMiddleware));
                    }
                    isSecureModeEnabled() {
                        return this.useSecureMode;
                    }
                    start() {
                        return o(this, void 0, void 0, function*() {
                            return new Promise((e, t) => {
                                ((this.server = this.app.listen(this.port, () => {
                                        (u.default.info(
                                                `Server running on port ${this.port}, 安全模式: ${this.useSecureMode ? "启用" : "禁用"}`,
                                            ),
                                            e());
                                    })),
                                    this.server.on("error", (e) => {
                                        u.default.error("Server error:", e);
                                    }));
                            });
                        });
                    }
                    stop() {
                        this.server &&
                            (this.server.close(), u.default.info("服务器已停止"));
                    }
                };
            },
            4759: function(e, t, n) {
                var o =
                    (this && this.__decorate) ||
                    function(e, t, n, o) {
                        var i,
                            r = arguments.length,
                            s =
                            r < 3 ?
                            t :
                            null === o ?
                            (o = Object.getOwnPropertyDescriptor(t, n)) :
                            o;
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.decorate
                        )
                            s = Reflect.decorate(e, t, n, o);
                        else
                            for (var a = e.length - 1; a >= 0; a--)
                                (i = e[a]) &&
                                (s = (r < 3 ? i(s) : r > 3 ? i(t, n, s) : i(t, n)) || s);
                        return (r > 3 && s && Object.defineProperty(t, n, s), s);
                    },
                    i =
                    (this && this.__metadata) ||
                    function(e, t) {
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.metadata
                        )
                            return Reflect.metadata(e, t);
                    },
                    r =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.ElectronApi = t.Protocols = void 0),
                    (t.InvokeType = a));
                const s = n(4922);

                function a(e) {
                    return function(t, n, o) {
                        Reflect.defineMetadata("invokeType", e, t, n);
                    };
                }
                (n(1321), (t.Protocols = {
                    INVOKE: "INVOKE",
                    TRRIGER: "TRRIGER"
                }));
                class c {
                    constructor() {
                        ((this.port = 0),
                            (this.windowId = ""),
                            (this.consumers = {}),
                            (this.apiName = this.getApiName()));
                    }
                    getEnvironment() {
                        try {
                            return null == navigator ||
                                navigator.userAgent.includes("Electron") ?
                                "Electron" :
                                "Browser";
                        } catch (e) {
                            return "Electron";
                        }
                    }
                    getNamespace() {}
                    getWindows() {
                        return this.windows;
                    }
                    setWindows(e) {
                        this.windows = e;
                    }
                    setPort(e) {
                        this.port = e;
                    }
                    getPort() {
                        return this.port;
                    }
                    setWindowId(e) {
                        this.windowId = e;
                    }
                    getWindowId() {
                        return this.windowId;
                    }
                    jsonToObject(e, t) {
                        return (0, s.plainToInstance)(e, t);
                    }
                    send(e, ...t) {
                        const n = this.buildKey(e);
                        this.sendMessage(n, ...t);
                    }
                    buildKey(e) {
                        let t = this.getNamespace(),
                            n = this.getApiName();
                        return (t && (n = t + "_" + n), `${n}.${e}`);
                    }
                    sendMessage(e, ...t) {
                        this.getWindows().webContents.send(e, ...t);
                    }
                    invokeApi(e, ...t) {
                        return r(this, void 0, void 0, function*() {
                            if ("Electron" == this.getEnvironment()) {
                                let n = this.getApiName();
                                return (
                                    this.getNamespace() && (n = this.getNamespace() + "_" + n),
                                    yield window[this.getApiName()][e](...t)
                                );
                            }
                            return {};
                        });
                    }
                    removeOnMessage(e, t) {
                        return r(this, void 0, void 0, function*() {
                            try {
                                return yield this.invokeApi("removeOnMessage", e, t);
                            } catch (e) {}
                        });
                    }
                    onMessage(e, t) {
                        return r(this, arguments, void 0, function*(e, t, n = void 0) {
                            if ("Electron" == this.getEnvironment()) {
                                let n = this.getApiName();
                                return (
                                    null != this.getNamespace() &&
                                    (n = this.getNamespace() + "_" + n),
                                    yield this.removeOnMessage(n, e),
                                        yield window[n][e](t)
                                );
                            }
                            return {};
                        });
                    }
                }
                ((t.ElectronApi = c),
                    o(
                        [
                            a(t.Protocols.INVOKE),
                            i("design:type", Function),
                            i("design:paramtypes", [String, String]),
                            i("design:returntype", Promise),
                        ],
                        c.prototype,
                        "removeOnMessage",
                        null,
                    ));
            },
            4922: (e) => {
                e.exports = require("class-transformer");
            },
            5317: (e) => {
                e.exports = require("child_process");
            },
            5335: function(e, t, n) {
                var o,
                    i =
                    (this && this.__createBinding) ||
                    (Object.create ?
                        function(e, t, n, o) {
                            void 0 === o && (o = n);
                            var i = Object.getOwnPropertyDescriptor(t, n);
                            ((i &&
                                    !("get" in i ?
                                        !t.__esModule :
                                        i.writable || i.configurable)) ||
                                (i = {
                                    enumerable: !0,
                                    get: function() {
                                        return t[n];
                                    },
                                }),
                                Object.defineProperty(e, o, i));
                        } :
                        function(e, t, n, o) {
                            (void 0 === o && (o = n), (e[o] = t[n]));
                        }),
                    r =
                    (this && this.__setModuleDefault) ||
                    (Object.create ?
                        function(e, t) {
                            Object.defineProperty(e, "default", {
                                enumerable: !0,
                                value: t,
                            });
                        } :
                        function(e, t) {
                            e.default = t;
                        }),
                    s =
                    (this && this.__importStar) ||
                    ((o = function(e) {
                            return (
                                (o =
                                    Object.getOwnPropertyNames ||
                                    function(e) {
                                        var t = [];
                                        for (var n in e)
                                            Object.prototype.hasOwnProperty.call(e, n) &&
                                            (t[t.length] = n);
                                        return t;
                                    }),
                                o(e)
                            );
                        }),
                        function(e) {
                            if (e && e.__esModule) return e;
                            var t = {};
                            if (null != e)
                                for (var n = o(e), s = 0; s < n.length; s++)
                                    "default" !== n[s] && i(t, e, n[s]);
                            return (r(t, e), t);
                        }),
                    a =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    c =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.start = void 0),
                    (t.createWindow = function(e, t) {
                        return a(this, void 0, void 0, function*() {
                            const n = new u.BrowserWindow({
                                width: 1e3,
                                height: 1e3,
                                webPreferences: {
                                    preload: l.join(__dirname, "preload.js"),
                                    contextIsolation: !0,
                                    webviewTag: !0,
                                    webSecurity: !1,
                                    nodeIntegration: !0,
                                },
                            });
                            return (n.loadURL(t), (n.webContents.windowId = e), n);
                        });
                    }));
                const u = n(4482),
                    l = n(6928);
                s(n(818)).config({
                    path: l.join(__dirname, ".env")
                });
                const d = n(5857),
                    f = n(2407),
                    h = c(n(1181)),
                    p = n(647),
                    g = n(181),
                    y = c(n(3423)),
                    v = n(835),
                    m = n(8312),
                    w = n(4393);

                function b() {
                    return a(this, void 0, void 0, function*() {
                        try {
                            const e = new y.default();
                            (0, g.init)(e);
                            const t = l.join(
                                    l.dirname(u.app.getAppPath()),
                                    "resource",
                                    "html",
                                    "index.html",
                                ),
                                n = new u.BrowserWindow({
                                    width: 500,
                                    height: 400,
                                    webPreferences: {
                                        preload: l.join(__dirname, "preload.js"),
                                        contextIsolation: !0,
                                        webviewTag: !0,
                                        webSecurity: !1,
                                        nodeIntegration: !0,
                                    },
                                });
                            (h.default.info("createDefaultWindow url: ", t),
                                n.loadFile(t),
                                (n.title = "x辅助"),
                                (0, d.setMainWindow)(n),
                                (function(e) {
                                    (u.app.isPackaged ||
                                        ((0, f.enableUpdateInDev)(),
                                            h.default.info("已在开发环境中启用更新检查")),
                                        (0, f.setupAutoUpdater)(e),
                                        h.default.info("应用启动: 立即检查更新..."),
                                        setTimeout(() => {
                                            (0, f.checkForUpdates)();
                                        }, 200),
                                        setInterval(
                                            () =>
                                            a(this, void 0, void 0, function*() {
                                                yield(0, f.checkForUpdates)();
                                            }),
                                            12e4,
                                        ));
                                })(n));
                        } catch (e) {
                            h.default.error("createDefaultWindow error", e);
                        }
                    });
                }
                (h.default.info("app load"),
                    (t.start = () => {
                        (u.app.on("ready", () =>
                                a(void 0, void 0, void 0, function*() {
                                    try {
                                        ((0, p.registerRpc)(),
                                            u.protocol.registerFileProtocol("localfile", (e, t) => {
                                                const n = e.url.replace(/^localfile:\/\//, "");
                                                try {
                                                    return t(decodeURIComponent(n));
                                                } catch (e) {
                                                    console.error(
                                                        "ERROR: registering local file protocol",
                                                        e,
                                                    );
                                                }
                                            }),
                                            h.default.info("应用已就绪，准备启动进程检测器"),
                                            (function() {
                                                try {
                                                    h.default.info("正在启动进程检测器...");
                                                    const e = !u.app.isPackaged,
                                                        t = new m.ProcessDetector(3e4, 2e4, e);
                                                    t.setDetectionCallback((e) => {
                                                        (h.default.warn(`检测到抓包工具: ${e.join(", ")}`),
                                                            h.default.warn(
                                                                "在开发环境中不会退出应用，但在生产环境中会退出",
                                                            ));
                                                    });
                                                    const n = t.getConfig();
                                                    (h.default.info(
                                                            `进程检测器配置: 间隔=${n.intervalMs}ms, 延迟=${n.startDelay}ms, 调试模式=${n.debugMode}`,
                                                        ),
                                                        t.start(),
                                                        setTimeout(() => {
                                                            h.default.info("运行手动检查...");
                                                            const e = t.runManualCheck();
                                                            e.length > 0 ?
                                                                h.default.warn(
                                                                    `手动检查检测到抓包工具: ${e.join(", ")}`,
                                                                ) :
                                                                h.default.info("手动检查未检测到抓包工具");
                                                        }, 5e3),
                                                        h.default.info(
                                                            `进程检测器已启动，将在${n.startDelay}ms后开始首次检查`,
                                                        ),
                                                        u.app.on("before-quit", () => {
                                                            (h.default.info("应用即将退出，正在停止进程检测器"),
                                                                t.stop());
                                                        }));
                                                } catch (e) {
                                                    h.default.error("启动进程检测器失败:", e);
                                                }
                                            })(),
                                            yield b());
                                    } catch (e) {
                                        h.default.error("ready createDefaultWindow error", e);
                                    }
                                }),
                            ),
                            u.app.on("window-all-closed", () => {
                                try {
                                    "darwin" !== process.platform && u.app.quit();
                                } catch (e) {
                                    h.default.error("window-all-closed error", e);
                                }
                            }),
                            u.app.on("activate", () =>
                                a(void 0, void 0, void 0, function*() {
                                    try {
                                        null === d.mainWindow && (yield b());
                                    } catch (e) {
                                        h.default.error("activate createDefaultWindow error", e);
                                    }
                                }),
                            ),
                            setTimeout(
                                () =>
                                a(void 0, void 0, void 0, function*() {
                                    (yield(0, v.initPlatform)(),
                                        yield(0, w.startSecureServer)());
                                }),
                                1e3,
                            ));
                    }));
            },
            5369: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.callbackDigg = c),
                    (t.healthCheck = u),
                    (t.updateBaseURL = l),
                    (t.setTimeout = d),
                    (t.setEncryption = f));
                const r = n(3669),
                    s = i(n(1181)),
                    a = new r.SecureHttpClient("http://localhost:33334/api", !0);

                function c(e, t) {
                    return o(this, void 0, void 0, function*() {
                        var n, o;
                        try {
                            s.default.info("[HttpInteraction] 调用 callbackDigg 接口");
                            const n = {
                                taskResponse: e,
                                data: t
                            };
                            return (yield a.post("/task/callback-digg", n)).data;
                        } catch (e) {
                            return (
                                s.default.error("[HttpInteraction] callbackDigg 调用失败:", e),
                                (null === (n = e.response) || void 0 === n ? void 0 : n.data) ?
                                e.response.data :
                                {
                                    success: !1,
                                    message: e.message || "请求失败",
                                    code: (null === (o = e.response) || void 0 === o ?
                                        void 0 :
                                        o.status) || 500,
                                }
                            );
                        }
                    });
                }

                function u() {
                    return o(this, void 0, void 0, function*() {
                        var e;
                        try {
                            return (
                                s.default.info("[HttpInteraction] 调用健康检查接口"),
                                (yield a.get("/health")).data
                            );
                        } catch (t) {
                            return (
                                s.default.error("[HttpInteraction] 健康检查失败:", t), {
                                    success: !1,
                                    message: t.message || "健康检查失败",
                                    code: (null === (e = t.response) || void 0 === e ?
                                        void 0 :
                                        e.status) || 500,
                                }
                            );
                        }
                    });
                }

                function l(e) {
                    ((a.getAxiosInstance().defaults.baseURL = e),
                        s.default.info(`[HttpInteraction] 已更新基础URL: ${e}`));
                }

                function d(e) {
                    ((a.getAxiosInstance().defaults.timeout = e),
                        s.default.info(`[HttpInteraction] 已设置超时时间: ${e}ms`));
                }

                function f(e) {
                    (a.setEncryption(e),
                        s.default.info("[HttpInteraction] 加密已" + (e ? "启用" : "禁用")));
                }
                (s.default.info("[HttpInteraction] 已创建加密HTTP客户端，通信已加密"),
                    (t.default = {
                        callbackDigg: c,
                        healthCheck: u,
                        updateBaseURL: l,
                        setTimeout: d,
                        setEncryption: f,
                    }));
            },
            5400: (e, t) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.getUrlParameter = function(e) {
                        const t = new URL(e);
                        return new URLSearchParams(t.search);
                    }));
            },
            5430: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.DiggMonitor = void 0),
                    (t.getDiggMonitor = function(e, t = !0) {
                        return (h.has(e) || h.set(e, new f(e, t)), h.get(e));
                    }),
                    (t.removeDiggMonitor = function(e) {
                        h.delete(e);
                    }));
                const r = i(n(8938)),
                    s = n(4482),
                    a = i(n(6928)),
                    c = n(9613),
                    u = i(n(1181)),
                    l = n(7390),
                    d = n(5369);
                class f {
                    constructor(e, t = !0) {
                        ((this.taskResponse = null),
                            (this.startFlag = !1),
                            (this.page = null),
                            (this.portId = e));
                    }
                    setTaskResponse(e) {
                        this.taskResponse = e;
                    }
                    getSessionDir() {
                        const e = s.app.getPath("userData");
                        return a.default.join(e, "resource", "dy", this.portId);
                    }
                    diggMonitor(e) {
                        return o(this, void 0, void 0, function*() {
                            yield e.route("**/*", (e) =>
                                o(this, void 0, void 0, function*() {
                                    const t = e.request();
                                    if ("xhr" == t.resourceType()) {
                                        const n = "web/commit/item/digg";
                                        if ((yield t.url()).includes(n) && this.taskResponse)
                                            return void(yield this.diggIntercept(e, t));
                                    }
                                    e.continue();
                                }),
                            );
                        });
                    }
                    confirmWindow(e) {
                        return o(this, void 0, void 0, function*() {
                            (yield(0, c.sleep)(2e3), u.default.info("confirmWindow start"));
                            const t = e.locator("#douyin-web-recommend-guide-mask");
                            if (!t) return;
                            u.default.info("confirmWindow found confirmWindow");
                            const n = yield t.isVisible();
                            u.default.info("confirmWindow is ", n);
                            let o = 30;
                            for (; n && o > 0;) {
                                const t = e
                                    .locator("#douyin-web-recommend-guide-mask button")
                                    .first();
                                if ((u.default.info("confirmButton is found "), t)) {
                                    const e = yield t.isVisible();
                                    if ((u.default.info("confirmButton is ", e), e))
                                        return (
                                            u.default.info("confirmButton click start"),
                                            yield t.click(),
                                                void u.default.info("confirmButton click end")
                                        );
                                }
                                (yield(0, c.sleep)(1e3), o--);
                            }
                        });
                    }
                    stopVideo(e) {
                        return o(this, void 0, void 0, function*() {
                            u.default.info("🎬 stopVideoV2 开始暂停视频");
                            try {
                                (yield this.confirmWindow(e),
                                    u.default.info("🔄 尝试备用方案：使用空格键暂停"),
                                    yield(0, c.sleep)(1e3),
                                        yield e.keyboard.press("Space"),
                                            yield(0, c.sleep)(1e3));
                                const t = e.locator(".xgplayer.xgplayer-playing").first();
                                if (
                                    yield t.evaluate((e) =>
                                        e.classList.contains("xgplayer-pause"),
                                    )
                                )
                                    return (
                                        u.default.info("✅ 备用方案成功：视频已通过空格键暂停"),
                                        !0
                                    );
                            } catch (e) {
                                console.error("❌ 备用方案也失败:", e);
                            }
                            return !1;
                        });
                    }
                    diggClick() {
                        return o(this, void 0, void 0, function*() {
                            if (!this.page) return;
                            const e = yield this.page
                                .locator("[data-e2e='video-player-digg']")
                                .first();
                            if (e) {
                                u.default.info("diggIcon is found");
                                let t = 30,
                                    n = yield e.isVisible();
                                for (; t > 0 && !n;)
                                    (yield(0, c.sleep)(1e3),
                                        (n = yield e.isVisible()),
                                        u.default.info("diggIcon is ", n),
                                        t--);
                                n
                                    ?
                                    (u.default.info("diggIcon click start"),
                                        yield e.click(),
                                            u.default.info("diggIcon click end")) :
                                    this.doCallback &&
                                    (u.default.error("diggIcon not visible"),
                                        yield this.doCallback(this.taskResponse, {
                                            status_code: 8,
                                        }));
                            }
                        });
                    }
                    start(e) {
                        return o(this, void 0, void 0, function*() {
                            u.default.info("diggMonitor start headless is ", e);
                            const t = yield(0, l.getEngine)(this.portId, e);
                            let n = t.getPage(),
                                o = !1;
                            if (n)
                                if (n.isClosed())
                                    (u.default.info("page is closed"),
                                        (n = yield t.init("https://www.douyin.com/?recommend=1")));
                                else {
                                    const e = yield n.url();
                                    (u.default.info("page is not close url is ", e),
                                        e.includes("https://www.douyin.com/?recommend=1") ?
                                        (o = !0) :
                                        (yield n.goto("https://www.douyin.com/?recommend=1"),
                                            u.default.info("page goto end")));
                                }
                            else n = yield t.init("https://www.douyin.com/?recommend=1");
                            return (
                                !!n &&
                                ((this.page = n),
                                    !!(yield this.checkAndInit(t, n)) &&
                                    (o ?
                                        (u.default.info("isCachePage is true"), !0) :
                                        (this.diggMonitor(n),
                                            yield this.stopVideo(n),
                                                yield(0, c.sleep)(5e3),
                                                    (this.startFlag = !0),
                                                    !0)))
                            );
                        });
                    }
                    isLogin(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield e
                                .locator("#douyin_login_comp_flat_panel")
                                .first();
                            if (t) {
                                let e = 10,
                                    n = yield t.isVisible();
                                for (; e > 0 && !n;) {
                                    if (
                                        (yield(0, c.sleep)(1e3),
                                            (n = yield t.isVisible()),
                                            u.default.info("is need login ", n),
                                            n)
                                    )
                                        return !1;
                                    e--;
                                }
                            }
                            return !0;
                        });
                    }
                    checkAndInit(e, t) {
                        return o(this, void 0, void 0, function*() {
                            if (yield this.isLogin(t))
                                return (u.default.info("isLogin is true"), !0);
                            const n = t.locator("#douyin-login-new-id .VWgr3GWj").first();
                            if (n) {
                                u.default.info("loginButton is found");
                                let t = 30,
                                    o = yield n.isVisible();
                                for (; t > 0 && !o;)
                                    (yield(0, c.sleep)(1e3),
                                        (o = yield n.isVisible()),
                                        u.default.info("loginButton is ", o),
                                        t--);
                                if (o) {
                                    const t = yield n.textContent();
                                    return !(
                                        !t ||
                                        !t.includes("一键登录") ||
                                        (u.default.info("one loginButton click start"),
                                            yield n.click(),
                                                yield(0, c.sleep)(5e3),
                                                    u.default.info("one loginButton click end"),
                                                    e.saveContextState(),
                                                    0)
                                    );
                                }
                                u.default.error("loginButton not visible");
                            }
                            return (u.default.info("loginButton not need to login"), !1);
                        });
                    }
                    stop() {
                        return o(this, void 0, void 0, function*() {
                            this.startFlag = !1;
                        });
                    }
                    diggIntercept(e, t) {
                        return o(this, void 0, void 0, function*() {
                            if (!this.taskResponse) return;
                            const n = yield t.url(),
                                o = yield t.headers();
                            try {
                                const i = yield t.postData(),
                                    s = new URLSearchParams(i || ""),
                                    a = Object.fromEntries(s.entries());
                                ((a.aweme_id = this.taskResponse.videoId), (a.type = "1"));
                                const c = new URLSearchParams(a).toString(),
                                    u = yield r.default.post(n, c, {
                                            headers: o
                                        }),
                                        l = yield u.data,
                                            d = JSON.stringify(l),
                                            f = yield u.headers;
                                (yield e.fulfill({
                                        status: 200,
                                        headers: f,
                                        contentType: "application/json",
                                        body: d,
                                    }),
                                    this.doCallback &&
                                    (yield this.doCallback(this.taskResponse, u.data)));
                            } catch (e) {
                                u.default.info("diggIntercept error", e);
                            }
                        });
                    }
                    doCallback(e, t) {
                        return o(this, void 0, void 0, function*() {
                            yield(0, d.callbackDigg)(e, t);
                        });
                    }
                }
                t.DiggMonitor = f;
                const h = new Map();
            },
            5857: (e, t) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.removeTargetWindow =
                        t.addTargetWindow =
                        t.pxxDetailWindow =
                        t.gatherWindow =
                        t.getGatherToolWindow =
                        t.setGatherToolWindow =
                        t.getGatherPreviewView =
                        t.setGatherPreviewView =
                        t.gatherPreviewView =
                        t.getGatherToolView =
                        t.setGatherToolView =
                        t.gatherToolView =
                        t.setUpdateWindow =
                        t.setMainWindow =
                        t.gatherToolWindow =
                        t.updateWindow =
                        t.mainWindow =
                        t.TargetWindow =
                        t.TargetView =
                        t.DEFAULT_WIDTH =
                        t.DEFAULT_HEIGHT =
                        void 0),
                    (t.getTargetWinodw = function(e) {
                        return o[e];
                    }),
                    (t.getGatherWindow = function() {
                        return t.gatherWindow;
                    }),
                    (t.setGatherWindow = function(e) {
                        t.gatherWindow = e;
                    }),
                    (t.getPxxDetailWindow = function() {
                        return t.pxxDetailWindow;
                    }),
                    (t.setPxxDetailWindow = function(e) {
                        t.pxxDetailWindow = e;
                    }),
                    (t.DEFAULT_HEIGHT = 500),
                    (t.DEFAULT_WIDTH = 500),
                    (t.TargetView = class {
                        constructor(e, t, n, o, i = !1) {
                            ((this.allowListener = !1),
                                (this.windowId = e),
                                (this.viewType = t),
                                (this.view = n),
                                o && (this.sessionInstance = o),
                                i && (this.allowListener = i));
                        }
                        init() {
                            this.sessionInstance && this.allowListener;
                        }
                        release() {
                            this.sessionInstance && this.allowListener;
                        }
                    }));
                class n {
                    constructor(e, t, n) {
                        ((this.windowId = e), (this.window = t), (this.views = n));
                    }
                    getView(e) {
                        for (let t of this.views)
                            if (t.viewType == e) return t.view;
                    }
                }
                ((t.TargetWindow = n), (t.mainWindow = null));
                const o = {};
                ((t.updateWindow = null),
                    (t.setMainWindow = (e) => {
                        t.mainWindow = e;
                    }),
                    (t.setUpdateWindow = (e) => {
                        t.updateWindow = e;
                    }),
                    (t.setGatherToolView = (e) => {
                        t.gatherToolView = e;
                    }),
                    (t.getGatherToolView = () => t.gatherToolView),
                    (t.setGatherPreviewView = (e) => {
                        t.gatherPreviewView = e;
                    }),
                    (t.getGatherPreviewView = () => t.gatherPreviewView),
                    (t.setGatherToolWindow = (e) => {
                        t.gatherToolWindow = e;
                    }),
                    (t.getGatherToolWindow = () => t.gatherToolWindow),
                    (t.addTargetWindow = (e, t, i) => {
                        const r = new n(e, t, i);
                        for (const e of i) e.init();
                        o[e] = r;
                    }),
                    (t.removeTargetWindow = (e) => {
                        if (e in o) {
                            const t = o[e];
                            for (const e of t.views) e.release();
                        }
                    }));
            },
            5883: (e) => {
                e.exports = require("playwright");
            },
            5898: (e, t, n) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.registerApiImpl = function() {
                        return (r.push(o.StoreApiImpl), r.push(i.InstallerImpl), r);
                    }));
                const o = n(1172),
                    i = n(3406),
                    r = [];
            },
            6238: (e) => {
                e.exports = require("electron-updater");
            },
            6261: (e) => {
                e.exports = require("events");
            },
            6836: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.runByPort = function(e, t) {
                        return o(this, void 0, void 0, function*() {
                            s.default.info("runByPort start", e);
                            const n = (0, r.getDiggMonitor)(e, t);
                            return yield n.start(t);
                        });
                    }),
                    (t.digg = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = (0, r.getDiggMonitor)(e.port);
                            return (t.setTaskResponse(e), yield t.diggClick(), !0);
                        });
                    }));
                const r = n(5430),
                    s = i(n(1181));
            },
            6928: (e) => {
                e.exports = require("path");
            },
            7252: (e) => {
                e.exports = require("express");
            },
            7390: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.getEngine = function(e) {
                        return o(this, arguments, void 0, function*(e, t = !0) {
                            const n = (function(e, t) {
                                return "dy_user_headless_" + e + "_" + t;
                            })(e, t);
                            if (r.has(n)) return r.get(n);
                            const o = new i.DyEngine(e, t);
                            return (r.set(n, o), o);
                        });
                    }),
                    (t.removeEngine = function(e) {
                        r.delete(e);
                    }),
                    (t.setEngine = function(e, t) {
                        r.set(e, t);
                    }));
                const i = n(3116),
                    r = new Map();
            },
            7531: function(e, t, n) {
                var o =
                    (this && this.__decorate) ||
                    function(e, t, n, o) {
                        var i,
                            r = arguments.length,
                            s =
                            r < 3 ?
                            t :
                            null === o ?
                            (o = Object.getOwnPropertyDescriptor(t, n)) :
                            o;
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.decorate
                        )
                            s = Reflect.decorate(e, t, n, o);
                        else
                            for (var a = e.length - 1; a >= 0; a--)
                                (i = e[a]) &&
                                (s = (r < 3 ? i(s) : r > 3 ? i(t, n, s) : i(t, n)) || s);
                        return (r > 3 && s && Object.defineProperty(t, n, s), s);
                    },
                    i =
                    (this && this.__metadata) ||
                    function(e, t) {
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.metadata
                        )
                            return Reflect.metadata(e, t);
                    },
                    r =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.StoreApi = void 0));
                const s = n(4759),
                    a = n(1969);
                class c extends s.ElectronApi {
                        getApiName() {
                            return "StoreApi";
                        }
                        isElectron() {
                            try {
                                return null == window;
                            } catch (e) {
                                return !0;
                            }
                        }
                        invokeApi(e, ...t) {
                            return r(this, void 0, void 0, function*() {
                                return "Electron" == this.getEnvironment() ?
                                    yield window[this.getApiName()][e](...t): localStorage[e](...t);
                            });
                        }
                        getItem(e) {
                            return r(this, void 0, void 0, function*() {
                                return this.isElectron() ?
                                    (0, a.getGlobal)(e) :
                                    yield this.invokeApi("getItem", e);
                            });
                        }
                        setItem(e, t) {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron())
                                    return yield this.invokeApi("setItem", e, t);
                                (0, a.setGlobal)(e, t);
                            });
                        }
                        removeItem(e) {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron())
                                    return yield this.invokeApi("removeItem", e);
                                (0, a.removeGlobal)(e);
                            });
                        }
                        clear() {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron()) return yield this.invokeApi("clear");
                                (0, a.clearGlobal)();
                            });
                        }
                        getPortItem(e) {
                            return r(this, void 0, void 0, function*() {
                                return this.isElectron() ?
                                    (0, a.get)(e) :
                                    yield this.invokeApi("getPortItem", e);
                            });
                        }
                        setPortItem(e, t) {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron())
                                    return yield this.invokeApi("setPortItem", e, t);
                                (0, a.set)(e, t);
                            });
                        }
                        removePortItem(e) {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron())
                                    return yield this.invokeApi("removePortItem", e);
                                (0, a.remove)(e);
                            });
                        }
                        clearPort() {
                            return r(this, void 0, void 0, function*() {
                                if (!this.isElectron()) return yield this.invokeApi("clearPort");
                                (0, a.clear)();
                            });
                        }
                        getPortConfig() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("getPortConfig");
                            });
                        }
                        copyPortConfig(e, t) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("copyPortConfig", e, t);
                            });
                        }
                        getAllPortConfigs() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("getAllPortConfigs");
                            });
                        }
                        getPortConfigByPort(e) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("getPortConfigByPort", e);
                            });
                        }
                        getAllStoreKeys() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("getAllStoreKeys");
                            });
                        }
                        setPortItemByPort(e, t, n) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("setPortItemByPort", e, t, n);
                            });
                        }
                        deletePortConfig(e) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("deletePortConfig", e);
                            });
                        }
                    }
                    ((t.StoreApi = c),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String, Object]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "setItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "removeItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "clear",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getPortItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String, Object]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "setPortItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [String]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "removePortItem",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "clearPort",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getPortConfig",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number, Number]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "copyPortConfig",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getAllPortConfigs",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getPortConfigByPort",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "getAllStoreKeys",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number, String, Object]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "setPortItemByPort",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", [Number]),
                                i("design:returntype", Promise),
                            ],
                            c.prototype,
                            "deletePortConfig",
                            null,
                        ));
            },
            8312: function(e, t, n) {
                var o,
                    i =
                    (this && this.__createBinding) ||
                    (Object.create ?
                        function(e, t, n, o) {
                            void 0 === o && (o = n);
                            var i = Object.getOwnPropertyDescriptor(t, n);
                            ((i &&
                                    !("get" in i ?
                                        !t.__esModule :
                                        i.writable || i.configurable)) ||
                                (i = {
                                    enumerable: !0,
                                    get: function() {
                                        return t[n];
                                    },
                                }),
                                Object.defineProperty(e, o, i));
                        } :
                        function(e, t, n, o) {
                            (void 0 === o && (o = n), (e[o] = t[n]));
                        }),
                    r =
                    (this && this.__setModuleDefault) ||
                    (Object.create ?
                        function(e, t) {
                            Object.defineProperty(e, "default", {
                                enumerable: !0,
                                value: t,
                            });
                        } :
                        function(e, t) {
                            e.default = t;
                        }),
                    s =
                    (this && this.__importStar) ||
                    ((o = function(e) {
                            return (
                                (o =
                                    Object.getOwnPropertyNames ||
                                    function(e) {
                                        var t = [];
                                        for (var n in e)
                                            Object.prototype.hasOwnProperty.call(e, n) &&
                                            (t[t.length] = n);
                                        return t;
                                    }),
                                o(e)
                            );
                        }),
                        function(e) {
                            if (e && e.__esModule) return e;
                            var t = {};
                            if (null != e)
                                for (var n = o(e), s = 0; s < n.length; s++)
                                    "default" !== n[s] && i(t, e, n[s]);
                            return (r(t, e), t);
                        }),
                    a =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.processDetector = t.ProcessDetector = void 0));
                const c = n(4482),
                    u = s(n(5317)),
                    l = s(n(857)),
                    d = a(n(1181)),
                    f = [
                        "charles",
                        "charlesproxy",
                        "charles.app",
                        "fiddler",
                        "fiddlereverywhere",
                        "fiddlercoreservice",
                        "burp",
                        "burpsuite",
                        "burpsuitecommunitye",
                        "burpsuitepro",
                        "proxyman",
                        "proxyman.app",
                        "wireshark",
                        "wireshark-gtk",
                        "tshark",
                        "mitmproxy",
                        "mitmweb",
                        "mitmdump",
                        "anyproxy",
                        "whistle",
                        "fiddle",
                        "zap.jar",
                        "owasp-zap",
                        "zaproxy",
                        "httpproxy",
                    ],
                    h = ["proxy", "http", "web"];
                class p {
                    constructor(e = 1e4, t = 5e3, n = !1) {
                        ((this.checkInterval = null),
                            (this.checkCount = 0),
                            (this.detectedTools = []),
                            (this.isRunning = !1),
                            (this.debugMode = !1),
                            (this.onDetectionCallback = null),
                            (this.intervalMs = e),
                            (this.startDelay = t),
                            (this.debugMode = n),
                            d.default.info(
                                `ProcessDetector initialized with interval: ${e}ms, start delay: ${t}ms, debug mode: ${n}`,
                            ));
                    }
                    setDetectionCallback(e) {
                        ((this.onDetectionCallback = e),
                            d.default.info("Detection callback set"));
                    }
                    setDebugMode(e) {
                        ((this.debugMode = e),
                            d.default.info("Debug mode " + (e ? "enabled" : "disabled")));
                    }
                    start() {
                        this.isRunning ?
                            d.default.info(
                                "ProcessDetector is already running, ignoring start request",
                            ) :
                            ((this.isRunning = !0),
                                d.default.info(
                                    `Starting proxy tool detection service (delay: ${this.startDelay}ms, interval: ${this.intervalMs}ms, debug mode: ${this.debugMode})`,
                                ),
                                d.default.info(
                                    `Process detector will check for these tools: ${f.join(", ")}`,
                                ),
                                d.default.info(
                                    `Process detector will exactly match these tools: ${h.join(", ")}`,
                                ),
                                d.default.info(
                                    `Current platform: ${l.platform()}, OS: ${l.type()} ${l.release()}`,
                                ),
                                d.default.info(
                                    `Delaying first check by ${this.startDelay}ms to allow app to initialize`,
                                ),
                                setTimeout(() => {
                                    (this.checkForProxyTools(),
                                        (this.checkInterval = setInterval(() => {
                                            this.checkForProxyTools();
                                        }, this.intervalMs)));
                                }, this.startDelay));
                    }
                    stop() {
                        this.isRunning ?
                            (this.checkInterval &&
                                (clearInterval(this.checkInterval),
                                    (this.checkInterval = null)),
                                (this.isRunning = !1),
                                d.default.info(
                                    `Stopped proxy tool detection service after ${this.checkCount} checks`,
                                )) :
                            d.default.info(
                                "ProcessDetector is not running, ignoring stop request",
                            );
                    }
                    isDetectionRunning() {
                        return this.isRunning;
                    }
                    getDetectedTools() {
                        return [...this.detectedTools];
                    }
                    getCheckCount() {
                        return this.checkCount;
                    }
                    getConfig() {
                        return {
                            intervalMs: this.intervalMs,
                            startDelay: this.startDelay,
                            debugMode: this.debugMode,
                        };
                    }
                    checkForProxyTools() {
                        try {
                            (this.checkCount++,
                                d.default.info(`Running proxy tool check #${this.checkCount}`));
                            const e = l.platform();
                            let t = !1;
                            switch (((this.detectedTools = []), e)) {
                                case "darwin":
                                    t = this.checkMacOS();
                                    break;
                                case "win32":
                                    t = this.checkWindows();
                                    break;
                                case "linux":
                                    t = this.checkLinux();
                                    break;
                                default:
                                    return void d.default.warn(
                                        `Unsupported platform for proxy tool detection: ${e}`,
                                    );
                            }
                            t
                                ?
                                (d.default.warn(
                                        `Proxy/capture tools detected: ${this.detectedTools.join(", ")}`,
                                    ),
                                    this.onDetectionCallback &&
                                    (d.default.info("Calling detection callback"),
                                        this.onDetectionCallback(this.detectedTools)),
                                    this.debugMode ?
                                    d.default.warn(
                                        "Debug mode is enabled, not exiting application",
                                    ) :
                                    (d.default.warn(
                                            "Exiting application for security reasons.",
                                        ),
                                        this.exitApp())) :
                                d.default.info(
                                    `Check #${this.checkCount} completed: No proxy tools detected`,
                                );
                        } catch (e) {
                            d.default.error(
                                `Error checking for proxy tools (check #${this.checkCount}):`,
                                e,
                            );
                        }
                    }
                    matchesProxyTool(e) {
                        const t = e.toLowerCase();
                        if (h.includes(t))
                            return (
                                d.default.warn(`Exact match found for process: ${e}`),
                                this.detectedTools.push(e),
                                !0
                            );
                        for (const n of f)
                            if (t.includes(n.toLowerCase()))
                                return (
                                    d.default.warn(
                                        `Proxy tool match found: ${n} in process: ${e}`,
                                    ),
                                    this.detectedTools.push(n),
                                    !0
                                );
                        return !1;
                    }
                    checkMacOS() {
                        try {
                            d.default.info("Running macOS process check: ps -A -o comm");
                            const e = u.execSync("ps -A -o comm").toString();
                            d.default.debug(`Process list length: ${e.length} characters`);
                            const t = e.split("\n").filter((e) => e.trim().length > 0);
                            d.default.debug(`Found ${t.length} processes running`);
                            let n = !1;
                            for (const e of t) this.matchesProxyTool(e.trim()) && (n = !0);
                            return n;
                        } catch (e) {
                            return (
                                d.default.error("Error checking macOS processes:", e),
                                !1
                            );
                        }
                    }
                    checkWindows() {
                        try {
                            d.default.info("Running Windows process check: tasklist /FO CSV");
                            const e = u.execSync("tasklist /FO CSV").toString();
                            d.default.debug(`Process list length: ${e.length} characters`);
                            const t = e.split("\n").slice(1);
                            d.default.debug(`Found ${t.length} processes running`);
                            let n = !1;
                            for (const e of t) {
                                if (!e.trim()) continue;
                                const t = e.match(/"([^"]+)"/);
                                if (t && t[1]) {
                                    const e = t[1].replace(".exe", "");
                                    this.matchesProxyTool(e) && (n = !0);
                                }
                            }
                            return n;
                        } catch (e) {
                            return (
                                d.default.error("Error checking Windows processes:", e),
                                !1
                            );
                        }
                    }
                    checkLinux() {
                        try {
                            d.default.info("Running Linux process check: ps -e -o comm");
                            const e = u.execSync("ps -e -o comm").toString();
                            d.default.debug(`Process list length: ${e.length} characters`);
                            const t = e.split("\n").filter((e) => e.trim().length > 0);
                            d.default.debug(`Found ${t.length} processes running`);
                            let n = !1;
                            for (const e of t) this.matchesProxyTool(e.trim()) && (n = !0);
                            return n;
                        } catch (e) {
                            return (
                                d.default.error("Error checking Linux processes:", e),
                                !1
                            );
                        }
                    }
                    runManualCheck() {
                        (d.default.info("Running manual proxy tool check"),
                            (this.detectedTools = []));
                        try {
                            const e = l.platform();
                            switch (e) {
                                case "darwin":
                                    this.checkMacOS();
                                    break;
                                case "win32":
                                    this.checkWindows();
                                    break;
                                case "linux":
                                    this.checkLinux();
                                    break;
                                default:
                                    d.default.warn(
                                        `Unsupported platform for proxy tool detection: ${e}`,
                                    );
                            }
                            return (
                                this.detectedTools.length > 0 ?
                                d.default.warn(
                                    `Manual check detected proxy tools: ${this.detectedTools.join(", ")}`,
                                ) :
                                d.default.info(
                                    "Manual check completed: No proxy tools detected",
                                ),
                                [...this.detectedTools]
                            );
                        } catch (e) {
                            return (
                                d.default.error(
                                    "Error running manual check for proxy tools:",
                                    e,
                                ),
                                []
                            );
                        }
                    }
                    exitApp() {
                        (this.stop(),
                            d.default.warn("Application exit triggered by ProcessDetector"),
                            d.default.warn(
                                `Detected proxy tools: ${this.detectedTools.join(", ")}`,
                            ),
                            d.default.warn(`Total checks performed: ${this.checkCount}`),
                            d.default.warn("Exiting with code 1"),
                            c.app.exit(1));
                    }
                }
                ((t.ProcessDetector = p), (t.processDetector = new p(1e4, 5e3, !1)));
            },
            8577: (e) => {
                e.exports = require("cors");
            },
            8749: (e) => {
                e.exports = require("crypto");
            },
            8922: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.MonitorChain =
                        t.MonitorResponse =
                        t.MonitorRequest =
                        t.Monitor =
                        void 0));
                const i = n(3466),
                    r = n(6261),
                    s = n(5400);
                class a {
                    constructor(e = 6e4) {
                        ((this.finishTag = !1),
                            (this.key = void 0),
                            (this.waitResolve = () => {}),
                            (this.hadListen = !1),
                            (this.allowRepeat = !1),
                            (this.startTag = !1),
                            (this.timeout = e),
                            (this.eventEmitter = new r.EventEmitter()),
                            (this.waitPromise = new Promise((e) => {
                                this.waitResolve = e;
                            })));
                    }
                    filter(e, t, n, i) {
                        return o(this, void 0, void 0, function*() {
                            return !1;
                        });
                    }
                    setMonitorTimeout(e) {
                        this.timeout = e;
                    }
                    setAllowRepeat(e) {
                        this.allowRepeat = e;
                    }
                    close() {
                        ((this.eventEmitter = new r.EventEmitter()),
                            (this.waitResolve = () => {}),
                            (this.startTag = !1),
                            (this.hadListen = !1),
                            (this.finishTag = !1),
                            this.setAllowRepeat(!1));
                    }
                    getItemKey(e) {}
                    getItemKeys(e) {
                        if (e) return this.getItemKey((0, s.getUrlParameter)(e));
                    }
                    doMatchResponse(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = e.url(),
                                n = e.request().method(),
                                o = yield e.request().allHeaders();
                            return yield this.isMatch(t, n, o);
                        });
                    }
                    setFinishTag(e) {
                        this.allowRepeat || (this.finishTag = e);
                    }
                    start() {
                        return o(this, void 0, void 0, function*() {
                            if (this.startTag) return;
                            const e = this;
                            (this.setFinishTag(!1),
                                this.listenEvent(),
                                setTimeout(
                                    () =>
                                    o(this, void 0, void 0, function*() {
                                        this.allowRepeat ||
                                            e.finishTag ||
                                            (yield e._doCallback(new i.DoorEntity(!1, {})),
                                                this.allowRepeat || e.setFinishTag(!0));
                                    }),
                                    this.timeout,
                                ),
                                (this.startTag = !0));
                        });
                    }
                    listenEvent() {
                        if (this.allowRepeat && !this.hadListen)
                            return (
                                this.eventEmitter.on(this.getEventKey(), (e) =>
                                    o(this, void 0, void 0, function*() {
                                        yield this.waitResolve(e);
                                    }),
                                ),
                                void(this.hadListen = !0)
                            );
                        this.onceEnvet();
                    }
                    needHeaderData() {
                        return !0;
                    }
                    needUrl() {
                        return !1;
                    }
                    needRequestBody() {
                        return !1;
                    }
                    needResponseHeaderData() {
                        return !1;
                    }
                    _doCallback(e) {
                        return o(
                            this,
                            arguments,
                            void 0,
                            function*(e, t = void 0, n = void 0) {
                                try {
                                    yield this.doCallback(e, t, n);
                                } finally {
                                    this.eventEmitter.emit(this.getEventKey(), e);
                                }
                            },
                        );
                    }
                    getEventKey() {
                        return this.constructor.name + "_actionCompleted";
                    }
                    doCallback(e) {
                        return o(
                            this,
                            arguments,
                            void 0,
                            function*(e, t = void 0, n = void 0) {},
                        );
                    }
                    onceEnvet() {
                        return o(this, void 0, void 0, function*() {
                            this.eventEmitter.once(this.getEventKey(), (e) =>
                                o(this, void 0, void 0, function*() {
                                    this.waitResolve(e);
                                }),
                            );
                        });
                    }
                    waitForAction() {
                        return o(this, void 0, void 0, function*() {
                            return yield this.waitPromise;
                        });
                    }
                }
                ((t.Monitor = a),
                    (t.MonitorRequest = class extends a {
                        constructor() {
                            (super(...arguments), (this.handler = void 0));
                        }
                        setHandler(e) {
                            this.handler = e;
                        }
                    }));
                const c = {};
                ((t.MonitorResponse = class extends a {
                        containerCookie(e) {
                            return !1;
                        }
                        needStoreContext(e, t) {
                            return !(function(e) {
                                return !!c[e];
                            })(e);
                        }
                        setStoreContext(e) {
                            !(function(e) {
                                c[e] = !0;
                            })(e);
                        }
                        getResponseData(e) {
                            return o(this, void 0, void 0, function*() {
                                const t = e.headers()["content-type"];
                                if (t.includes("application/json")) {
                                    const t = yield e.json();
                                    return new i.DoorEntity(!0, t.data);
                                }
                                return t && t.includes("text/html") ?
                                    new i.DoorEntity(!0, yield e.text()) :
                                    new i.DoorEntity(!0, yield e.body());
                            });
                        }
                    }),
                    (t.MonitorChain = class {
                        constructor() {
                            ((this.monitors = []),
                                (this.data = {}),
                                (this.monitors = this.initMonitors()));
                        }
                        getKey() {
                            return this.constructor.name;
                        }
                        append(e) {
                            this.monitors.push(e);
                        }
                        start() {
                            return o(this, void 0, void 0, function*() {
                                for (const e of this.monitors) yield e.start();
                            });
                        }
                        getMonitors() {
                            return this.monitors;
                        }
                        waitForAction() {
                            return o(this, void 0, void 0, function*() {
                                const e = {};
                                let t = !0;
                                for (let n of this.monitors) {
                                    const o = yield n.waitForAction();
                                    if (!o.code) {
                                        if (o.validateUrl) return o;
                                        t = !1;
                                    }
                                    e[n.getKey()] = o.data;
                                }
                                return new i.DoorEntity(t, e);
                            });
                        }
                        getItemKey(e) {}
                        getItemKeys(e) {
                            return this.getItemKey((0, s.getUrlParameter)(e));
                        }
                    }));
            },
            8938: (e) => {
                e.exports = require("axios");
            },
            8969: function(e, t, n) {
                var o =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    },
                    i =
                    (this && this.__importDefault) ||
                    function(e) {
                        return e && e.__esModule ? e : {
                            default: e
                        };
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.openLogin = f),
                    (t.awaitByLoginResult = function(e) {
                        return o(this, arguments, void 0, function*(e, t = !0) {
                            r.default.info(
                                "awaitByLoginResult port is ",
                                e,
                                " headless is ",
                                t,
                            );
                            let n = yield(0, d.getEngine)(e, t);
                            const i = yield(function(e) {
                                return o(this, void 0, void 0, function*() {
                                    return e.getPage() || (yield e.init());
                                });
                            })(n);
                            try {
                                if (!i) return;
                                const e = new s.DyLoginMonitor();
                                e.setMonitorTimeout(12e4);
                                let a = null;
                                if (
                                    ((a = t ?
                                            yield n.openWaitMonitor(
                                                i,
                                                "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                                e, {},
                                            ): yield n.openWaitMonitor(
                                                i,
                                                "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                                e, {},
                                                f,
                                            )),
                                        !a.getCode())
                                )
                                    return new c.DoorEntity(!1, "登录失败");
                                const l = a.data;
                                if (0 != l.status_code) return new c.DoorEntity(!1, "登录失败");
                                const d = l.sec_uid,
                                    h = l.uid;
                                let p = yield(function(e) {
                                    return o(this, void 0, void 0, function*() {
                                        const t = yield e
                                            .locator(".IGPVd8vQ .GMEdHsXq .arnSiSbK")
                                            .first();
                                        if (t && (yield t.isVisible())) {
                                            const e = yield t.textContent();
                                            if (e) return (r.default.info("user name is ", e), e);
                                        }
                                        return null;
                                    });
                                })(i);
                                (r.default.info("nickName is ", p), p || (p = ""));
                                const g = new u.DyUser(h, d, !0, p);
                                yield(function(e) {
                                    return o(this, void 0, void 0, function*() {
                                        const t = yield e
                                            .locator(".trust-login-switch-button")
                                            .first();
                                        if (t) {
                                            r.default.info("button is visible");
                                            let n = 10;
                                            for (; n > 0;) {
                                                const n = yield t.isVisible(),
                                                    o = yield t.getAttribute("class");
                                                if (
                                                    (r.default.info(
                                                            "button is visible ",
                                                            n,
                                                            " classList is ",
                                                            o,
                                                        ),
                                                        n)
                                                )
                                                    return o && o.includes("uncheck") ?
                                                        (r.default.info(
                                                                "button is visible and classList is ",
                                                                o,
                                                            ),
                                                            yield t.click(),
                                                                yield e.waitForTimeout(3e3),
                                                                    void r.default.info("button click")) :
                                                        void r.default.info(
                                                            "button is visible and classList is not uncheck",
                                                        );
                                                yield e.waitForTimeout(1e3);
                                            }
                                        }
                                    });
                                })(i);
                                const y = yield(function(e) {
                                    return o(this, void 0, void 0, function*() {
                                        const t = e.cookie;
                                        if (!t) return null;
                                        const n = t.split(";").find((e) => e.includes("sessionid"));
                                        return {
                                            sessionId: null == n ? void 0 : n.split("=")[1],
                                            cookie: t,
                                        };
                                    });
                                })(a.getHeaderData());
                                return (
                                    y && ((g.sessionId = y.sessionId), (g.token = y.cookie)),
                                    yield n.saveContextState(),
                                        r.default.info("登录成功 ", h),
                                        new c.DoorEntity(!0, g)
                                );
                            } catch (e) {
                                return (
                                    r.default.error("awaitByLoginResult error", e),
                                    new c.DoorEntity(!1, "登录失败")
                                );
                            }
                        });
                    }),
                    (t.openUserInfo = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = new a.DyEngine(e, !1);
                            yield t.init(
                                "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                            );
                        });
                    }),
                    (t.awaitByLoginResultByQR = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield(0, d.getEngine)(e);
                            let n = t.getPage();
                            n
                                ?
                                yield n.goto(
                                    "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                ): (n = yield t.init(
                                    "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                ));
                            try {
                                if (!n) return;
                                if (yield y(t, n))
                                    return (
                                        r.default.info("awaitByLoginResultByQR hadLogin is true"),
                                        new c.DoorEntity(!0, "登录成功")
                                    );
                                new s.DyLoginMonitor().setMonitorTimeout(12e4);
                                const e = yield(function(e) {
                                    return o(this, void 0, void 0, function*() {
                                        try {
                                            yield e.waitForTimeout(3e3);
                                            const t = [
                                                "#share-login-guide img",
                                                "#douyin-login-new-id .WxXE_uiP img",
                                                "#animate_qrcode_container img",
                                            ];
                                            let n = 120,
                                                o = null,
                                                i = !1;
                                            const s = new Map();
                                            for (; n > 0 && !i;) {
                                                for (const n of t) {
                                                    if (i) continue;
                                                    const t = yield p(s, n, e);
                                                    if (t)
                                                        if (
                                                            ((i = yield g(0, t)),
                                                                r.default.info(n, " isVisible is ", i),
                                                                i)
                                                        )
                                                            (r.default.info("showImgElement is ", n),
                                                                (o = t));
                                                        else if (((i = yield g(0, t)), yield h(e)))
                                                        return new c.DoorEntity(!0);
                                                }
                                                (yield e.waitForTimeout(1e3), n--);
                                            }
                                            if (o) {
                                                let e = null;
                                                return (
                                                    i && (e = yield o.getAttribute("src")),
                                                    e ?
                                                    new c.DoorEntity(!0, {
                                                        qrCode: e
                                                    }) :
                                                    (r.default.error("src is null"),
                                                        new c.DoorEntity(!1, "获取二维码失败"))
                                                );
                                            }
                                        } catch (e) {
                                            r.default.error("openLoginPageAction error", e);
                                        }
                                        const t = yield h(e);
                                        return (
                                            r.default.info("isLogin is ", t),
                                            t ? new c.DoorEntity(!0) : new c.DoorEntity(!1, void 0)
                                        );
                                    });
                                })(n);
                                if (!e.getCode()) return e;
                                const i = e.data.qrCode;
                                return i ?
                                    new c.DoorEntity(!0, {
                                        qrCode: i
                                    }) :
                                    new c.DoorEntity(!0, "登录成功");
                            } catch (e) {
                                return (
                                    r.default.error("awaitByLoginResult error", e),
                                    new c.DoorEntity(!1, "获取二维码失败")
                                );
                            }
                        });
                    }),
                    (t.checkAgainValidate = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield(0, d.getEngine)(e);
                            if (!t) return new c.DoorEntity(!1, "检测失败");
                            const n = t.getPage();
                            if (!n) return new c.DoorEntity(!1, "检测失败");
                            yield n.waitForTimeout(2e3);
                            const o = yield n
                                .locator("#uc-second-verify .vxBq1Jp1n2QfyF17sA9K")
                                .first();
                            if (o) {
                                let e = yield o.isVisible(),
                                    i = 200;
                                for (
                                    r.default.info("validate button isVisible is ", e); !e && i > 0;

                                ) {
                                    if (
                                        (yield n.waitForTimeout(1e3),
                                            (e = yield o.isVisible()),
                                            yield h(n))
                                    )
                                        return (
                                            yield t.saveContextState(),
                                                new c.DoorEntity(!0, {
                                                    isLogin: !0,
                                                    needSmsCode: !1
                                                })
                                        );
                                    (r.default.info("validate button isVisible is ", e), i--);
                                }
                                if (e)
                                    return (
                                        r.default.info("validate button start click"),
                                        yield o.click(),
                                            r.default.info("validate button end click"),
                                            new c.DoorEntity(!0, {
                                                isLogin: !1,
                                                needSmsCode: !0
                                            })
                                    );
                            }
                            return (yield h(n)) ?
                                new c.DoorEntity(!0, {
                                    isLogin: !0,
                                    needSmsCode: !1
                                }) :
                                new c.DoorEntity(!1, "未知错误");
                        });
                    }),
                    (t.loginBySmsCode = function(e, t) {
                        return o(this, void 0, void 0, function*() {
                            const n = yield(0, d.getEngine)(e);
                            if (!n) return new c.DoorEntity(!1, "登录失败");
                            const o = n.getPage();
                            if (!o) return new c.DoorEntity(!1, "登录失败");
                            const i = new l.DyLoginSmsValidateMonitor(),
                                r = yield n.openWaitMonitor(o, void 0, i, {}, m, t);
                            return r.getCode() ?
                                (yield n.saveContextState(),
                                    yield o.waitForTimeout(2e3),
                                        new c.DoorEntity(!0, "登录成功")) :
                                new c.DoorEntity(!1, r.data);
                        });
                    }),
                    (t.loginBySmsCodeAction = m),
                    (t.againSendSms = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield(0, d.getEngine)(e);
                            if (!t) return new c.DoorEntity(!1, "发送失败");
                            const n = t.getPage();
                            if (!n) return new c.DoorEntity(!1, "发送失败");
                            const o = yield n.locator(".MPtA2zFhBqMwM58QgANJ").first();
                            if (o) {
                                let e = 10,
                                    t = yield o.isVisible();
                                for (r.default.info("button is visible ", t); !t && e > 0;)
                                    (yield n.waitForTimeout(1e3),
                                        (t = yield o.isVisible()),
                                        r.default.info("button is visible ", t),
                                        e--);
                                return t ?
                                    (r.default.info("button sms login start click"),
                                        yield o.click(),
                                            r.default.info("button sms login end click"),
                                            new c.DoorEntity(!0, "发送成功")) :
                                    new c.DoorEntity(!1, "发送失败");
                            }
                            return new c.DoorEntity(!1, "发送失败");
                        });
                    }),
                    (t.smsLoginInit = function(e) {
                        return o(this, void 0, void 0, function*() {
                            const t = yield(0, d.getEngine)(e);
                            let n = t.getPage();
                            return (
                                n ?
                                yield n.goto(
                                        "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                    ): (n = yield t.init(
                                        "https://www.douyin.com/user/self?from_tab_name=main&showTab=like",
                                    )),
                                    n ?
                                    (yield y(t, n)) ?
                                    new c.DoorEntity(!0, "登录成功") :
                                    new c.DoorEntity(!1, "加载验证码页面") :
                                    new c.DoorEntity(!1, "初始化失败")
                            );
                        });
                    }),
                    (t.getValidateCodeByPhone = function(e, t) {
                        return o(this, void 0, void 0, function*() {
                            const n = yield(0, d.getEngine)(e);
                            if (!n) return new c.DoorEntity(!1, "获取验证码引擎错误");
                            const o = n.getPage();
                            if (!o) return new c.DoorEntity(!1, "获取验证码引擎错误");
                            const i = new l.SendSmsCodeMonitor(),
                                s = yield o.locator(".GmnLSQ7P").first(),
                                    a = yield s.isVisible();
                            if (
                                (r.default.info("getValidateCodeByPhone isInputVisible is ", a),
                                    a)
                            ) {
                                (yield s.fill(t), yield o.waitForTimeout(2e3));
                                const e = yield o.locator(".MVPbkVeT").first(),
                                    a = yield e.isVisible();
                                if (
                                    (r.default.info(
                                            "getValidateCodeByPhone isButtonVisible is ",
                                            a,
                                        ),
                                        a)
                                ) {
                                    yield e.click();
                                    const t = yield n.openWaitMonitor(o, void 0, i, {});
                                    return t.getCode() ?
                                        new c.DoorEntity(!0, "发送验证码成功") :
                                        (r.default.error(
                                                "getValidateCodeByPhone result is ",
                                                t.data,
                                            ),
                                            new c.DoorEntity(!1, t.data));
                                }
                            }
                            return new c.DoorEntity(
                                !1,
                                "发送验证码异常,请重新打开验证码登录",
                            );
                        });
                    }),
                    (t.loginByPhone = function(e, t) {
                        return o(this, void 0, void 0, function*() {
                            const n = yield(0, d.getEngine)(e);
                            if (!n) return new c.DoorEntity(!1, "登录引擎错误");
                            const o = n.getPage();
                            if (!o) return new c.DoorEntity(!1, "登录引擎错误");
                            const i = yield o.locator("#button-input").first();
                            if (!i || !(yield i.isVisible()))
                                return new c.DoorEntity(!1, "登录未填充验证码");
                            (yield i.fill(t), yield o.waitForTimeout(2e3));
                            const s = new l.DyLoginSmsMonitor(),
                                a = yield o.locator(".VWgr3GWj").first(),
                                    u = yield a.isVisible();
                            if (
                                (r.default.info("loginByPhone isLoginButtonVisible is ", u), u)
                            ) {
                                const e = yield n.openWaitMonitor(o, void 0, s, {}, w, a);
                                return e.code ?
                                    (yield o.waitForTimeout(2e3),
                                        yield n.saveContextState(),
                                            new c.DoorEntity(!0, "登录成功")) :
                                    (r.default.error("loginByPhone result is ", e.data),
                                        new c.DoorEntity(!1, e.data));
                            }
                            return new c.DoorEntity(!1, "登录失败");
                        });
                    }),
                    (t.loginByPhoneAction = w));
                const r = i(n(1181)),
                    s = n(2749),
                    a = n(3116),
                    c = n(3466),
                    u = n(1357),
                    l = n(2686),
                    d = n(7390);

                function f(e) {
                    return o(this, void 0, void 0, function*() {
                        setTimeout(
                            () =>
                            o(this, void 0, void 0, function*() {
                                r.default.info("start click");
                                let t = yield e.locator("#Cn2CzO_Q .bYaAzfVn").first();
                                if (t) {
                                    let n = 30;
                                    for (; n > 0;) {
                                        if (yield t.isVisible()) {
                                            (yield t.click(), yield e.waitForTimeout(1e3));
                                            const n = e.locator("#login-panel-new");
                                            if (n && (yield n.isVisible())) return;
                                        }
                                        (yield e.waitForTimeout(1e3), n--);
                                    }
                                }
                            }),
                            1e3,
                        );
                    });
                }

                function h(e) {
                    return o(this, void 0, void 0, function*() {
                        const t = yield(function(e) {
                            return o(this, void 0, void 0, function*() {
                                let t = yield e.locator("#user-name");
                                if (t && (yield t.isVisible())) {
                                    const e = yield t.textContent();
                                    if (e) return (r.default.info("user name is ", e), e);
                                }
                                if (
                                    ((t = yield e
                                            .locator("#user_detail_element .GMEdHsXq")
                                            .first()),
                                        t && (yield t.isVisible()))
                                ) {
                                    const e = yield t.textContent();
                                    if (e) return (r.default.info("user name is ", e), e);
                                }
                                return null;
                            });
                        })(e);
                        return !!t;
                    });
                }

                function p(e, t, n) {
                    return o(this, void 0, void 0, function*() {
                        if (e.has(t)) return e.get(t);
                        const o = yield n.locator(t).first();
                        return (e.set(t, o), o);
                    });
                }

                function g(e, t) {
                    return o(this, void 0, void 0, function*() {
                        return !!t && !!(yield t.isVisible());
                    });
                }

                function y(e, t) {
                    return o(this, void 0, void 0, function*() {
                        if (yield h(t))
                            return (
                                r.default.info("checkAndOneLogin isLogin is true by nickname"),
                                !0
                            );
                        const n = yield(function(e) {
                            return o(this, void 0, void 0, function*() {
                                try {
                                    const t = yield e.waitForSelector("#RkbQLUok button", {
                                        state: "visible",
                                        timeout: 5e3,
                                    });
                                    return !(
                                        !t ||
                                        !(yield t.isVisible()) ||
                                        (r.default.info("checkLoginButtonExist start click"),
                                            yield t.click(),
                                                yield e.waitForTimeout(1e3),
                                                    r.default.info("checkLoginButtonExist end click"),
                                                    0)
                                    );
                                } catch (e) {
                                    return !1;
                                }
                            });
                        })(t);
                        if (n) {
                            r.default.info(
                                "checkAndOneLogin isLogin is true by loginButtonExist",
                            );
                            const n = yield(function(e, t) {
                                return o(this, void 0, void 0, function*() {
                                    const n = t.locator("#douyin-login-new-id .VWgr3GWj").first();
                                    if (n) {
                                        r.default.info("oneLoginButton is found");
                                        let o = 5,
                                            i = yield n.isVisible();
                                        for (; o > 0 && !i;)
                                            (yield t.waitForTimeout(1e3),
                                                (i = yield n.isVisible()),
                                                r.default.info("oneLoginButton is ", i),
                                                o--);
                                        if (i) {
                                            const o = yield n.textContent();
                                            if (o && o.includes("一键登录")) {
                                                r.default.info("one loginButton click start");
                                                const o = new s.OneLoginMonitor();
                                                return (yield e.openWaitMonitor(
                                                        t,
                                                        void 0,
                                                        o, {},
                                                        v,
                                                        n,
                                                    )).getCode() ?
                                                    (yield e.saveContextState(),
                                                        yield t.waitForTimeout(2e3),
                                                            r.default.info("one login success"),
                                                            !0) :
                                                    (r.default.info("one login not support"), !1);
                                            }
                                            return !1;
                                        }
                                    }
                                    return !1;
                                });
                            })(e, t);
                            if (n)
                                return (
                                    r.default.info(
                                        "checkAndOneLogin isLogin is true by retryOneLogin",
                                    ),
                                    !0
                                );
                        }
                        return !1;
                    });
                }

                function v(e, t) {
                    return o(this, void 0, void 0, function*() {
                        (yield e.waitForTimeout(2e3), yield t.click());
                    });
                }

                function m(e, t) {
                    return o(this, void 0, void 0, function*() {
                        const n = yield e.locator("#button-input").first();
                        if (n) {
                            (yield n.fill(t), yield e.waitForTimeout(2e3));
                            const o = yield e
                                .locator("#uc-second-verify .qT0K_Szs1Fa804Pg2hhR")
                                .first();
                            if (o) {
                                let t = 10,
                                    n = yield o.isVisible();
                                for (r.default.info("button is visible ", n); !n && t > 0;)
                                    (r.default.info("button is visible ", n),
                                        (n = yield o.isVisible()),
                                        yield e.waitForTimeout(1e3),
                                            t--);
                                if (n)
                                    return (
                                        r.default.info("button sms login start click"),
                                        yield o.click(),
                                            void r.default.info("button sms login end click")
                                    );
                            }
                            r.default.info("button is not visible");
                        }
                        return new c.DoorEntity(!1, "登录失败");
                    });
                }

                function w(e, t) {
                    return o(this, void 0, void 0, function*() {
                        (yield e.waitForTimeout(2e3), yield t.click());
                    });
                }
                new Map();
            },
            9154: function(e, t, n) {
                var o =
                    (this && this.__decorate) ||
                    function(e, t, n, o) {
                        var i,
                            r = arguments.length,
                            s =
                            r < 3 ?
                            t :
                            null === o ?
                            (o = Object.getOwnPropertyDescriptor(t, n)) :
                            o;
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.decorate
                        )
                            s = Reflect.decorate(e, t, n, o);
                        else
                            for (var a = e.length - 1; a >= 0; a--)
                                (i = e[a]) &&
                                (s = (r < 3 ? i(s) : r > 3 ? i(t, n, s) : i(t, n)) || s);
                        return (r > 3 && s && Object.defineProperty(t, n, s), s);
                    },
                    i =
                    (this && this.__metadata) ||
                    function(e, t) {
                        if (
                            "object" == typeof Reflect &&
                            "function" == typeof Reflect.metadata
                        )
                            return Reflect.metadata(e, t);
                    },
                    r =
                    (this && this.__awaiter) ||
                    function(e, t, n, o) {
                        return new(n || (n = Promise))(function(i, r) {
                            function s(e) {
                                try {
                                    c(o.next(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function a(e) {
                                try {
                                    c(o.throw(e));
                                } catch (e) {
                                    r(e);
                                }
                            }

                            function c(e) {
                                var t;
                                e.done ?
                                    i(e.value) :
                                    ((t = e.value),
                                        t instanceof n ?
                                        t :
                                        new n(function(e) {
                                            e(t);
                                        })).then(s, a);
                            }
                            c((o = o.apply(e, t || [])).next());
                        });
                    };
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.InstallerApi = void 0));
                const s = n(4759);
                class a extends s.ElectronApi {
                        getApiName() {
                            return "InstallerApi";
                        }
                        update() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("update");
                            });
                        }
                        cancelUpdate() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("cancelUpdate");
                            });
                        }
                        install() {
                            return r(this, void 0, void 0, function*() {
                                return yield this.invokeApi("install");
                            });
                        }
                        onMonitorDownloadProgress(e) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.onMessage("onMonitorDownloadProgress", e);
                            });
                        }
                        onMonitorUpdateDownloaded(e) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.onMessage("onMonitorUpdateDownloaded", e);
                            });
                        }
                        onMonitorUpdateDownloadedError(e) {
                            return r(this, void 0, void 0, function*() {
                                return yield this.onMessage("onMonitorUpdateDownloadedError", e);
                            });
                        }
                    }
                    ((t.InstallerApi = a),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "update",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "cancelUpdate",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.INVOKE),
                                i("design:type", Function),
                                i("design:paramtypes", []),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "install",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.TRRIGER),
                                i("design:type", Function),
                                i("design:paramtypes", [Function]),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "onMonitorDownloadProgress",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.TRRIGER),
                                i("design:type", Function),
                                i("design:paramtypes", [Function]),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "onMonitorUpdateDownloaded",
                            null,
                        ),
                        o(
                            [
                                (0, s.InvokeType)(s.Protocols.TRRIGER),
                                i("design:type", Function),
                                i("design:paramtypes", [Function]),
                                i("design:returntype", Promise),
                            ],
                            a.prototype,
                            "onMonitorUpdateDownloadedError",
                            null,
                        ));
            },
            9613: (e, t) => {
                (Object.defineProperty(t, "__esModule", {
                        value: !0
                    }),
                    (t.isDev = t.sleep = t.getThemeBg = void 0),
                    (t.bubbleSort = function*(e) {
                        const t = e.length;
                        for (let n = 0; n < t - 1; n++)
                            for (let o = 0; o <= t - n - 1; o++)
                                (e[o] > e[o + 1] && ([e[o], e[o + 1]] = [e[o + 1], e[o]]),
                                    yield e.map((e, t) => ({
                                        value: e,
                                        swap: t === o || t === o + 1,
                                    })));
                        return e;
                    }),
                    (t.selectionSort = function*(e) {
                        for (let t = 0; t < e.length; t++) {
                            let n = t;
                            for (let o = t + 1; o < e.length; o++) e[o] < e[n] && (n = o);
                            (n !== t && ([e[t], e[n]] = [e[n], e[t]]),
                                yield e.map((e, o) => ({
                                    value: e,
                                    swap: o === n || o === t,
                                })));
                        }
                        return e;
                    }),
                    (t.insertionSort = function*(e) {
                        const t = e.length;
                        let n, o;
                        for (let i = 1; i < t; i++) {
                            for (n = i - 1, o = e[i]; n >= 0 && e[n] > o;)
                                ((e[n + 1] = e[n]), n--);
                            ((e[n + 1] = o),
                                yield e.map((e, t) => ({
                                    value: e,
                                    swap: t === n + 1 || t === i,
                                })));
                        }
                        return e;
                    }),
                    (t.isDev = !1),
                    (t.getThemeBg = (e = !0) =>
                        e ?
                        {
                            backgroundColor: "rgba(73, 82, 123, 0.3)",
                            color: "rgba(255, 255, 255, 1)",
                        } :
                        {
                            backgroundColor: "rgba(255, 255, 255, 1)",
                            color: "rgba(0, 0, 0, 1)",
                        }),
                    (t.sleep = (e = 2e3) =>
                        new Promise((t, n) => {
                            setTimeout(() => {
                                t(1);
                            }, e);
                        })));
            },
            9896: (e) => {
                e.exports = require("fs");
            },
        },
        t = {};
    (0,
        (function n(o) {
            var i = t[o];
            if (void 0 !== i) return i.exports;
            var r = (t[o] = {
                exports: {}
            });
            return (e[o].call(r.exports, r, r.exports, n), r.exports);
        })(5335).start)();
})();